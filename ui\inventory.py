from PyQt5.QtWidgets import (Q<PERSON><PERSON>t, Q<PERSON>oxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QFormLayout, QTextEdit, QHeaderView, QMessageBox,
                            QDialog, QComboBox, QGroupBox, QDateEdit, QDoubleSpinBox,
                            QSpinBox, QTabWidget, QSplitter, QFrame, QMenu, QAction, QSizePolicy,
                            QTextBrowser, QListWidget, QListWidgetItem)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QIcon, QFont, QColor

from database import (Inventory, Supplier, get_session)
from utils import (show_error_message, show_info_message, show_confirmation_message,
                    qdate_to_datetime, datetime_to_qdate, format_currency, format_quantity)
import datetime

from ui.unified_styles import (UnifiedStyles, StyledButton, StyledGroupBox,
                                StyledTable, <PERSON>d<PERSON><PERSON>l, StyledTabWidget)

class InventoryItemDialog(QDialog):
    """نافذة حوار لإضافة أو تعديل عنصر في المخزون"""

    def __init__(self, parent=None, item=None, session=None):
        super().__init__(parent)
        self.item = item
        self.session = session
        self.init_ui()

    def init_ui(self):
        # إعداد نافذة الحوار
        if self.item:
            self.setWindowTitle("تعديل عنصر المخزون")
        else:
            self.setWindowTitle("إضافة عنصر جديد للمخزون")

        self.setMinimumWidth(600)
        self.setMinimumHeight(500)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # إنشاء نموذج معلومات العنصر مع تخطيط محسن
        form_group = QGroupBox("معلومات العنصر")
        form_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)

        # تخطيط رئيسي للنموذج
        main_form_layout = QVBoxLayout()

        # الصف الأول: اسم العنصر والفئة
        first_row_layout = QHBoxLayout()

        # اسم العنصر
        name_layout = QVBoxLayout()
        name_layout.addWidget(QLabel("اسم العنصر:"))
        self.name_edit = QLineEdit()
        self.name_edit.setStyleSheet("padding: 8px; font-size: 13px; border: 2px solid #bdc3c7; border-radius: 4px;")
        if self.item:
            self.name_edit.setText(self.item.name)
        name_layout.addWidget(self.name_edit)
        first_row_layout.addLayout(name_layout)

        # الفئة
        category_layout = QVBoxLayout()
        category_layout.addWidget(QLabel("الفئة:"))
        self.category_combo = QComboBox()
        self.category_combo.setStyleSheet("padding: 8px; font-size: 13px; border: 2px solid #bdc3c7; border-radius: 4px;")
        categories = ["دهانات", "سيراميك", "أخشاب", "أدوات صحية", "أدوات كهربائية", "مواد بناء", "أخرى"]
        for category in categories:
            self.category_combo.addItem(category)
        if self.item and self.item.category:
            index = self.category_combo.findText(self.item.category)
            if index >= 0:
                self.category_combo.setCurrentIndex(index)
        category_layout.addWidget(self.category_combo)
        first_row_layout.addLayout(category_layout)

        main_form_layout.addLayout(first_row_layout)

        # الصف الثاني: وحدة القياس والكمية والحد الأدنى
        second_row_layout = QHBoxLayout()

        # وحدة القياس
        unit_layout = QVBoxLayout()
        unit_layout.addWidget(QLabel("وحدة القياس:"))
        self.unit_combo = QComboBox()
        self.unit_combo.setStyleSheet("padding: 8px; font-size: 13px; border: 2px solid #bdc3c7; border-radius: 4px;")
        units = ["قطعة", "متر", "متر مربع", "كيلوجرام", "لتر", "طن", "علبة", "كرتون", "لوح"]
        for unit in units:
            self.unit_combo.addItem(unit)
        if self.item and self.item.unit:
            index = self.unit_combo.findText(self.item.unit)
            if index >= 0:
                self.unit_combo.setCurrentIndex(index)
        unit_layout.addWidget(self.unit_combo)
        second_row_layout.addLayout(unit_layout)

        # الكمية
        quantity_layout = QVBoxLayout()
        quantity_layout.addWidget(QLabel("الكمية:"))
        self.quantity_edit = QDoubleSpinBox()
        self.quantity_edit.setStyleSheet("padding: 8px; font-size: 13px; border: 2px solid #bdc3c7; border-radius: 4px;")
        self.quantity_edit.setRange(0, 100000)
        self.quantity_edit.setDecimals(0)
        if self.item:
            self.quantity_edit.setValue(self.item.quantity or 0)
        quantity_layout.addWidget(self.quantity_edit)
        second_row_layout.addLayout(quantity_layout)

        # الحد الأدنى
        min_quantity_layout = QVBoxLayout()
        min_quantity_layout.addWidget(QLabel("الحد الأدنى:"))
        self.min_quantity_edit = QDoubleSpinBox()
        self.min_quantity_edit.setStyleSheet("padding: 8px; font-size: 13px; border: 2px solid #bdc3c7; border-radius: 4px;")
        self.min_quantity_edit.setRange(0, 10000)
        self.min_quantity_edit.setDecimals(0)
        if self.item:
            self.min_quantity_edit.setValue(self.item.min_quantity or 0)
        min_quantity_layout.addWidget(self.min_quantity_edit)
        second_row_layout.addLayout(min_quantity_layout)

        main_form_layout.addLayout(second_row_layout)

        # الصف الثالث: سعر التكلفة وسعر البيع
        third_row_layout = QHBoxLayout()

        # سعر التكلفة
        cost_layout = QVBoxLayout()
        cost_layout.addWidget(QLabel("سعر التكلفة:"))
        self.cost_price_edit = QDoubleSpinBox()
        self.cost_price_edit.setStyleSheet("padding: 8px; font-size: 13px; border: 2px solid #bdc3c7; border-radius: 4px;")
        self.cost_price_edit.setRange(0, 1000000)
        self.cost_price_edit.setDecimals(0)
        self.cost_price_edit.setSingleStep(10)
        if self.item:
            self.cost_price_edit.setValue(self.item.cost_price or 0)
        cost_layout.addWidget(self.cost_price_edit)
        third_row_layout.addLayout(cost_layout)

        # سعر البيع
        selling_layout = QVBoxLayout()
        selling_layout.addWidget(QLabel("سعر البيع:"))
        self.selling_price_edit = QDoubleSpinBox()
        self.selling_price_edit.setStyleSheet("padding: 8px; font-size: 13px; border: 2px solid #bdc3c7; border-radius: 4px;")
        self.selling_price_edit.setRange(0, 1000000)
        self.selling_price_edit.setDecimals(0)
        self.selling_price_edit.setSingleStep(10)
        if self.item:
            self.selling_price_edit.setValue(self.item.selling_price or 0)
        selling_layout.addWidget(self.selling_price_edit)
        third_row_layout.addLayout(selling_layout)

        main_form_layout.addLayout(third_row_layout)

        # الصف الرابع: المورد وموقع التخزين
        fourth_row_layout = QHBoxLayout()

        # المورد
        supplier_layout = QVBoxLayout()
        supplier_layout.addWidget(QLabel("المورد:"))
        self.supplier_combo = QComboBox()
        self.supplier_combo.setStyleSheet("padding: 8px; font-size: 13px; border: 2px solid #bdc3c7; border-radius: 4px;")
        self.supplier_combo.addItem("-- اختر مورد --", None)
        if self.session:
            suppliers = self.session.query(Supplier).all()
            for supplier in suppliers:
                self.supplier_combo.addItem(supplier.name, supplier.id)
        if self.item and self.item.supplier_id:
            index = self.supplier_combo.findData(self.item.supplier_id)
            if index >= 0:
                self.supplier_combo.setCurrentIndex(index)
        supplier_layout.addWidget(self.supplier_combo)
        fourth_row_layout.addLayout(supplier_layout)

        # موقع التخزين
        location_layout = QVBoxLayout()
        location_layout.addWidget(QLabel("موقع التخزين:"))
        self.location_edit = QLineEdit()
        self.location_edit.setStyleSheet("padding: 8px; font-size: 13px; border: 2px solid #bdc3c7; border-radius: 4px;")
        if self.item:
            self.location_edit.setText(self.item.location or "")
        location_layout.addWidget(self.location_edit)
        fourth_row_layout.addLayout(location_layout)

        main_form_layout.addLayout(fourth_row_layout)

        form_group.setLayout(main_form_layout)

        # أزرار الحفظ والإلغاء
        button_layout = QHBoxLayout()

        self.save_button = QPushButton("حفظ")
        self.save_button.clicked.connect(self.accept)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)

        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)

        button_layout.addStretch()
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)

        # تجميع التخطيط النهائي
        main_layout.addWidget(form_group)
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

    def get_data(self):
        """الحصول على بيانات عنصر المخزون من النموذج"""
        name = self.name_edit.text().strip()
        category = self.category_combo.currentText()
        unit = self.unit_combo.currentText()
        quantity = self.quantity_edit.value()
        min_quantity = self.min_quantity_edit.value()
        cost_price = self.cost_price_edit.value()
        selling_price = self.selling_price_edit.value()
        supplier_id = self.supplier_combo.currentData()
        location = self.location_edit.text().strip()

        # التحقق من صحة البيانات
        if not name:
            show_error_message("خطأ", "يجب إدخال اسم العنصر")
            return None

        if selling_price < cost_price:
            if not show_confirmation_message("تحذير", "سعر البيع أقل من سعر التكلفة. هل تريد المتابعة؟"):
                return None

        return {
            'name': name,
            'category': category,
            'unit': unit,
            'quantity': quantity,
            'min_quantity': min_quantity,
            'cost_price': cost_price,
            'selling_price': selling_price,
            'supplier_id': supplier_id,
            'location': location,
            'notes': '',  # إزالة الملاحظات
            'last_updated': datetime.datetime.now()
        }

class InventoryMainWidget(QWidget):
    """واجهة إدارة المخزون الرئيسية مع تبويبات"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        print("🔧 بدء إنشاء واجهة المخازن...")
        try:
            self.init_ui()
            print("✅ تم إنشاء واجهة المخازن بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إنشاء واجهة المخازن: {str(e)}")
            self.create_emergency_ui()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للعمال
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(8)

        # إنشاء تبويبات للمخزون والمشتريات مع تنسيق مطابق للمشاريع والعمال
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 3px solid #000000;
                border-radius: 8px;
                background: #ffffff;
                margin-top: -1px;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e2e8f0, stop:0.5 #cbd5e1, stop:1 #94a3b8);
                color: #1e293b;
                border: 3px solid #000000;
                border-bottom: none;
                border-radius: 8px 8px 0 0;
                padding: 8px 32px;
                margin: 2px;
                font-size: 16px;
                font-weight: bold;
                min-width: 400px;
                max-width: 600px;
                min-height: 35px;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4285f4, stop:0.5 #1a73e8, stop:1 #1557b0);
                color: white;
                border: 3px solid #000000;
                border-bottom: none;
                margin-top: -1px;
                padding: 9px 32px;
                font-size: 17px;
                min-height: 35px;
                max-height: 40px;
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5a67d8, stop:0.5 #667eea, stop:1 #e0c3fc);
                border: 2px solid #000000;
            }
        """)

        # إنشاء تبويب المخزون مع أيقونة مثل المشاريع
        print("🔧 إنشاء تبويب المخزون...")
        try:
            self.inventory_widget = InventoryWidget(self.session)
            self.tabs.addTab(self.inventory_widget, "📦 إدارة المخزون")
            print("✅ تم إنشاء تبويب المخزون بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إنشاء تبويب المخزون: {str(e)}")
            # إنشاء تبويب بسيط للمخزون
            simple_inventory = self.create_simple_inventory_widget()
            self.tabs.addTab(simple_inventory, "📦 إدارة المخزون")

        # إنشاء تبويب المشتريات مع أيقونة مثل المشاريع
        print("🔧 إنشاء تبويب المشتريات...")
        try:
            from ui.purchases import PurchasesWidget
            self.purchases_widget = PurchasesWidget(self.session)
            self.tabs.addTab(self.purchases_widget, "🛒 إدارة المشتريات")
            print("✅ تم تحميل تبويب المشتريات المتطور بنجاح")
        except Exception as e:
            print(f"❌ خطأ في تحميل المشتريات المتطورة: {str(e)}")
            # إضافة تبويب بسيط للمشتريات
            self.purchases_widget = self.create_simple_purchases_widget()
            self.tabs.addTab(self.purchases_widget, "🛒 إدارة المشتريات")
            print("✅ تم إنشاء تبويب المشتريات البسيط بنجاح")

        # إنشاء تبويب المبيعات مع أيقونة مثل المشاريع
        print("🔧 إنشاء تبويب المبيعات...")
        try:
            from ui.sales import SalesWidget
            self.sales_widget = SalesWidget(self.session)
            self.tabs.addTab(self.sales_widget, "💰 إدارة المبيعات")
            print("✅ تم تحميل تبويب المبيعات المتطور بنجاح")
        except Exception as e:
            print(f"❌ خطأ في تحميل المبيعات المتطورة: {str(e)}")
            # إضافة تبويب بسيط للمبيعات
            self.sales_widget = self.create_simple_sales_widget()
            self.tabs.addTab(self.sales_widget, "💰 إدارة المبيعات")
            print("✅ تم إنشاء تبويب المبيعات البسيط بنجاح")

        # إضافة التبويبات إلى التخطيط الرئيسي
        main_layout.addWidget(self.tabs, 1)  # إعطاء التبويبات أولوية في التمدد

        self.setLayout(main_layout)

    def create_emergency_ui(self):
        """إنشاء واجهة طوارئ بسيطة"""
        print("🚨 إنشاء واجهة طوارئ للمخازن...")
        layout = QVBoxLayout()

        # عنوان
        title = QLabel("🏪 إدارة المخازن")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #3b82f6;
                color: white;
                padding: 15px;
                border-radius: 10px;
                margin: 10px;
            }
        """)

        # رسالة
        message = QLabel("حدث خطأ في تحميل واجهة المخازن.\nسيتم إصلاح هذه المشكلة قريباً.")
        message.setAlignment(Qt.AlignCenter)
        message.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #6b7280;
                padding: 30px;
                background-color: #f9fafb;
                border-radius: 8px;
                margin: 20px;
            }
        """)

        layout.addWidget(title)
        layout.addWidget(message)
        layout.addStretch()

        self.setLayout(layout)
        print("✅ تم إنشاء واجهة طوارئ للمخازن")

    def create_simple_inventory_widget(self):
        """إنشاء تبويب مخزون بسيط"""
        widget = QWidget()
        layout = QVBoxLayout()

        # عنوان
        title = QLabel("📦 المخزون")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #059669;
                color: white;
                padding: 10px;
                border-radius: 8px;
                margin: 10px;
            }
        """)

        # رسالة
        message = QLabel("سيتم تطوير هذا القسم قريباً...")
        message.setAlignment(Qt.AlignCenter)
        message.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6b7280;
                padding: 20px;
            }
        """)

        layout.addWidget(title)
        layout.addWidget(message)
        layout.addStretch()

        widget.setLayout(layout)
        return widget

    def create_simple_purchases_widget(self):
        """إنشاء تبويب مشتريات بسيط"""
        widget = QWidget()
        layout = QVBoxLayout()

        # عنوان
        title = QLabel("🛒 المشتريات")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #3b82f6;
                color: white;
                padding: 10px;
                border-radius: 8px;
                margin: 10px;
            }
        """)

        # رسالة
        message = QLabel("سيتم تطوير هذا القسم قريباً...")
        message.setAlignment(Qt.AlignCenter)
        message.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6b7280;
                padding: 20px;
            }
        """)

        layout.addWidget(title)
        layout.addWidget(message)
        layout.addStretch()

        widget.setLayout(layout)
        return widget

    def create_simple_sales_widget(self):
        """إنشاء تبويب مبيعات بسيط"""
        widget = QWidget()
        layout = QVBoxLayout()

        # عنوان
        title = QLabel("💰 المبيعات")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #10b981;
                color: white;
                padding: 10px;
                border-radius: 8px;
                margin: 10px;
            }
        """)

        # رسالة
        message = QLabel("سيتم تطوير هذا القسم قريباً...")
        message.setAlignment(Qt.AlignCenter)
        message.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6b7280;
                padding: 20px;
            }
        """)

        layout.addWidget(title)
        layout.addWidget(message)
        layout.addStretch()

        widget.setLayout(layout)
        return widget

class InventoryWidget(QWidget):
    """واجهة إدارة المخزون"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()
        try:
            self.refresh_data()
        except Exception as e:
            print(f"❌ خطأ في تحديث بيانات المخزون: {str(e)}")
            # إنشاء بيانات تجريبية بسيطة
            self.create_sample_data()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للموردين والعمال والمصروفات والإيرادات والمشاريع والفواتير
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من جميع الاتجاهات لاستغلال المساحة
        main_layout.setSpacing(3)  # تقليل المسافات بين العناصر

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للفواتير
        title_label = QLabel("📦 إدارة المخزون المتطورة - نظام شامل ومتقدم لإدارة المخزون مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للموردين
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث بالاسم، الفئة، المورد أو الموقع...")
        self.search_edit.textChanged.connect(self.filter_inventory)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 3px solid rgba(139, 92, 246, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(240, 249, 255, 0.9),
                    stop:1 rgba(224, 242, 254, 0.85));
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QLineEdit:hover {
                border: 3px solid rgba(124, 58, 237, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
            }
        """)

        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px;
                font-size: 20px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(91, 33, 182, 0.9),
                    stop:1 rgba(76, 29, 149, 0.8));
                border: 3px solid rgba(91, 33, 182, 0.9);
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
            }
        """)
        search_button.clicked.connect(self.filter_inventory)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية مطورة مطابقة للفواتير
        filter_label = QLabel("🎯 فئة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إضافة حقل تصفية حسب الفئة محسن
        self.category_filter = QComboBox()
        self.category_filter.addItem("جميع الفئات", None)
        categories = ["دهانات", "سيراميك", "أخشاب", "أدوات صحية", "أدوات كهربائية", "مواد بناء", "أخرى"]
        for category in categories:
            self.category_filter.addItem(category, category)
        self.category_filter.currentIndexChanged.connect(self.filter_inventory)
        self.category_filter.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                min-width: 120px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QComboBox:hover {
                border: 3px solid rgba(124, 58, 237, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
            }
            QComboBox:focus {
                border: 3px solid rgba(139, 92, 246, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(240, 249, 255, 0.9),
                    stop:1 rgba(224, 242, 254, 0.85));
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid rgba(96, 165, 250, 0.8);
                width: 8px;
                height: 8px;
                border-radius: 4px;
                background: rgba(96, 165, 250, 0.8);
            }
        """)

        # تسمية المخزون المنخفض مطابقة للفواتير
        stock_label = QLabel("📉 مخزون:")
        stock_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        stock_label.setAlignment(Qt.AlignCenter)

        # إضافة حقل تصفية حسب المخزون المنخفض محسن
        self.low_stock_filter = QComboBox()
        self.low_stock_filter.addItem("الكل", None)
        self.low_stock_filter.addItem("المخزون المنخفض فقط", "low")
        self.low_stock_filter.currentIndexChanged.connect(self.filter_inventory)
        self.low_stock_filter.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                min-width: 120px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QComboBox:hover {
                border: 3px solid rgba(124, 58, 237, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
            }
            QComboBox:focus {
                border: 3px solid rgba(139, 92, 246, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(240, 249, 255, 0.9),
                    stop:1 rgba(224, 242, 254, 0.85));
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid rgba(96, 165, 250, 0.8);
                width: 8px;
                height: 8px;
                border-radius: 4px;
                background: rgba(96, 165, 250, 0.8);
            }
        """)

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.category_filter, 1, Qt.AlignVCenter)
        search_layout.addWidget(stock_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.low_stock_filter, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء جدول المخزون المتطور والمحسن
        self.create_advanced_inventory_table()

        main_layout.addWidget(top_frame)
        main_layout.addWidget(self.inventory_table, 1)  # إعطاء الجدول أولوية في التمدد

        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للموردين
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار بأحجام متناسقة حسب الترتيب الجديد مطابق للموردين

        # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة
        self.add_button = QPushButton("➕ إضافة عنصر")
        self.style_advanced_button(self.add_button, 'emerald', has_menu=True)  # أخضر زمردي مميز مع قائمة
        self.add_button.clicked.connect(self.add_item)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')  # أزرق سماوي متطور مطابق للفواتير
        self.edit_button.clicked.connect(self.edit_item)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')  # أحمر تحذيري
        self.delete_button.clicked.connect(self.delete_item)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # تصميم حديث ومتطور
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الثانية - العمليات المتقدمة
        self.view_button = QPushButton("👁️ عرض التفاصيل ▼")
        self.style_advanced_button(self.view_button, 'indigo', has_menu=True)  # بنفسجي للتفاصيل
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة لعرض التفاصيل مطابقة للأقسام الأخرى
        from ui.unified_styles import UnifiedStyles
        view_menu = QMenu(self)
        view_menu.setStyleSheet(UnifiedStyles.get_menu_style('indigo', 'normal'))

        view_details_action = QAction("👁️ عرض التفاصيل", self)
        view_details_action.triggered.connect(self.view_item)
        view_menu.addAction(view_details_action)

        stock_history_action = QAction("📊 تاريخ المخزون", self)
        stock_history_action.triggered.connect(self.view_stock_history)
        view_menu.addAction(stock_history_action)

        supplier_info_action = QAction("🏪 معلومات المورد", self)
        supplier_info_action.triggered.connect(self.view_supplier_info)
        view_menu.addAction(supplier_info_action)

        stock_alerts_action = QAction("⚠️ تنبيهات المخزون", self)
        stock_alerts_action.triggered.connect(self.view_stock_alerts)
        view_menu.addAction(stock_alerts_action)

        self.view_button.setMenu(view_menu)

        self.adjust_button = QPushButton("📊 تعديل الكمية")
        self.style_advanced_button(self.adjust_button, 'orange')  # برتقالي للكميات
        self.adjust_button.clicked.connect(self.adjust_quantity)
        self.adjust_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'info', has_menu=True)  # لون متسق مع نظام الألوان
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة للتصدير مطابقة للأقسام الأخرى
        export_menu = QMenu(self)
        export_menu.setStyleSheet(UnifiedStyles.get_menu_style('info', 'normal'))

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(self.export_to_excel)
        export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(self.export_to_pdf)
        export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(self.export_to_csv)
        export_menu.addAction(csv_action)

        json_action = QAction("🔗 تصدير إلى JSON", self)
        json_action.triggered.connect(self.export_to_json)
        export_menu.addAction(json_action)

        self.export_button.setMenu(export_menu)

        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')  # وردي للإحصائيات
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إجمالي المخزون مطور ليتشابه مع الفواتير
        self.total_label = QLabel("إجمالي العناصر: 0")
        self.total_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                padding: 8px 16px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #064e3b,
                    stop:0.1 #047857,
                    stop:0.9 #065f46,
                    stop:1 #10b981);
                border: 5px solid #10b981;
                border-radius: 20px;
                min-height: 34px;
                max-height: 38px;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                           2px 2px 4px rgba(0, 0, 0, 0.7),
                           1px 1px 2px rgba(0, 0, 0, 0.5);
                box-shadow: 0 8px 20px rgba(16, 185, 129, 0.6),
                           inset 0 3px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(16, 185, 129, 0.6),
                           0 0 40px rgba(255, 255, 255, 0.1);
                letter-spacing: 0.5px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        """)
        self.total_label.setAlignment(Qt.AlignCenter)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.adjust_button)
        actions_layout.addWidget(self.export_button)
        actions_layout.addWidget(self.statistics_button)
        actions_layout.addWidget(self.total_label)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        # تجميع التخطيط النهائي
        main_layout.addWidget(bottom_frame)

        self.setLayout(main_layout)

    def create_advanced_inventory_table(self):
        """إنشاء جدول المخزون المتطور والمحسن مطابق للموردين"""
        self.inventory_table = QTableWidget()
        self.inventory_table.setColumnCount(9)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "🆔 الرقم التسلسلي",
            "📦 اسم العنصر",
            "🏷️ الفئة",
            "📊 الكمية",
            "📏 الوحدة",
            "⚠️ الحد الأدنى",
            "💰 سعر التكلفة",
            "💵 سعر البيع",
            "🏢 المورد"
        ]
        self.inventory_table.setHorizontalHeaderLabels(headers)

        # تحسين عرض الأعمدة
        header = self.inventory_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # الرقم
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم العنصر
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # الفئة
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # الكمية
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # الوحدة
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # الحد الأدنى
        header.setSectionResizeMode(6, QHeaderView.Fixed)  # سعر التكلفة
        header.setSectionResizeMode(7, QHeaderView.Fixed)  # سعر البيع
        header.setSectionResizeMode(8, QHeaderView.Stretch)  # المورد

        # تحديد عرض الأعمدة الثابتة
        self.inventory_table.setColumnWidth(0, 80)   # الرقم
        self.inventory_table.setColumnWidth(2, 100)  # الفئة
        self.inventory_table.setColumnWidth(3, 80)   # الكمية
        self.inventory_table.setColumnWidth(4, 80)   # الوحدة
        self.inventory_table.setColumnWidth(5, 100)  # الحد الأدنى
        self.inventory_table.setColumnWidth(6, 120)  # سعر التكلفة
        self.inventory_table.setColumnWidth(7, 120)  # سعر البيع

        self.inventory_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.inventory_table.setSelectionMode(QTableWidget.SingleSelection)
        self.inventory_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.inventory_table.setAlternatingRowColors(True)

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للمصروفات والإيرادات والفواتير
        self.inventory_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للمصروفات والإيرادات والفواتير
        header = self.inventory_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                vertical-align: middle !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للمصروفات والإيرادات والفواتير
        self.inventory_table.verticalHeader().setDefaultSectionSize(45)
        self.inventory_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        # إضافة العلامة المائية للجدول مطابقة للفواتير
        self.add_watermark_to_inventory_table()

    def add_watermark_to_inventory_table(self):
        """إضافة علامة مائية لجدول المخزون مطابقة للفواتير"""
        try:
            # إنشاء العلامة المائية مثل الفواتير
            watermark = QLabel("Smart Finish", self.inventory_table)
            watermark.setAlignment(Qt.AlignCenter)

            # تنسيق العلامة المائية مكبرة أكثر للتوحيد مع الباقي
            font = QFont("Arial", 150, QFont.Bold)
            watermark.setFont(font)

            # تطبيق تصميم محسن للعلامة المائية - أكثر وضوحاً لكن لا يؤثر على البيانات
            watermark.setStyleSheet("""
                QLabel {
                    color: rgba(71, 85, 105, 0.35);
                    background: transparent;
                    border: none;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
                    font-weight: bold;
                }
            """)

            # إصلاح مشكلة التفاعل - العلامة المائية لا تتداخل مع النقرات
            watermark.setAttribute(Qt.WA_TransparentForMouseEvents, True)
            watermark.setEnabled(False)  # تعطيل التفاعل مع العلامة المائية
            watermark.lower()  # وضع العلامة المائية في الخلف

            # تحسين إضافي للشفافية والوضوح
            watermark.setWindowOpacity(0.6)  # شفافية محسنة

            # تحديد موضع وحجم العلامة المائية مع تحسين الموضع
            def update_watermark_geometry():
                if self.inventory_table.isVisible():
                    rect = self.inventory_table.rect()
                    # تحسين الموضع ليكون في المنتصف مع مساحة أفضل
                    watermark.setGeometry(rect.x() + 50, rect.y() + 100,
                                        rect.width() - 100, rect.height() - 200)
                    watermark.lower()  # وضع العلامة المائية في الخلف دائماً
                    watermark.setAttribute(Qt.WA_TransparentForMouseEvents, True)

            # ربط تحديث الموضع بتغيير حجم الجدول
            original_resize_event = self.inventory_table.resizeEvent
            def custom_resize_event(event):
                original_resize_event(event)
                update_watermark_geometry()
            self.inventory_table.resizeEvent = custom_resize_event

            # تحديث أولي للموضع
            update_watermark_geometry()

        except Exception as e:
            print(f"خطأ في إضافة العلامة المائية للمخزون: {str(e)}")

    def refresh_data(self):
        """تحديث بيانات المخزون في الجدول"""
        # الحصول على جميع عناصر المخزون من قاعدة البيانات
        inventory_items = self.session.query(Inventory).order_by(Inventory.name).all()

        # إذا لم توجد بيانات، إنشاء بيانات تجريبية
        if not inventory_items:
            print("🧪 لا توجد بيانات في المخزون، إنشاء بيانات تجريبية...")
            self.create_sample_inventory_data()
            inventory_items = self.session.query(Inventory).order_by(Inventory.name).all()

        self.populate_table(inventory_items)
        self.update_summary(inventory_items)

    def populate_table(self, items):
        """ملء جدول المخزون بالبيانات"""
        self.inventory_table.setRowCount(0)

        for row, item in enumerate(items):
            self.inventory_table.insertRow(row)

            # 1. الرقم مع أيقونة
            id_item = QTableWidgetItem(f"🔢 {item.id}")
            id_item.setTextAlignment(Qt.AlignCenter)
            id_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
            id_item.setForeground(QColor("#1e40af"))
            id_item.setToolTip(f"🔢 رقم العنصر: {item.id}")
            self.inventory_table.setItem(row, 0, id_item)

            # 2. اسم العنصر مع أيقونة
            name_item = QTableWidgetItem(f"📦 {item.name}")
            name_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
            name_item.setForeground(QColor("#059669"))
            name_item.setToolTip(f"📦 اسم العنصر: {item.name}")
            self.inventory_table.setItem(row, 1, name_item)

            # 3. الفئة مع أيقونة
            category = item.category or "غير محدد"
            category_icons = {
                'مواد خام': '🧱',
                'أدوات': '🔧',
                'معدات': '⚙️',
                'مستلزمات': '📋',
                'قطع غيار': '🔩'
            }
            category_icon = category_icons.get(category, '📂')
            category_item = QTableWidgetItem(f"{category_icon} {category}")
            category_item.setFont(QFont("Segoe UI", 10, QFont.Normal))
            category_item.setForeground(QColor("#7c3aed"))
            category_item.setToolTip(f"{category_icon} الفئة: {category}")
            self.inventory_table.setItem(row, 2, category_item)

            # 4. الكمية مع أيقونة وتحذير
            quantity_text = format_quantity(item.quantity)
            if item.quantity <= item.min_quantity:
                quantity_item = QTableWidgetItem(f"⚠️ {quantity_text}")
                quantity_item.setBackground(QColor(255, 200, 200))
                quantity_item.setForeground(QColor("#dc2626"))
                quantity_item.setToolTip(f"⚠️ تحذير: الكمية منخفضة! الكمية الحالية: {quantity_text}")
            else:
                quantity_item = QTableWidgetItem(f"✅ {quantity_text}")
                quantity_item.setBackground(QColor(200, 255, 200))
                quantity_item.setForeground(QColor("#059669"))
                quantity_item.setToolTip(f"✅ الكمية متوفرة: {quantity_text}")

            quantity_item.setTextAlignment(Qt.AlignCenter)
            quantity_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
            self.inventory_table.setItem(row, 3, quantity_item)

            # 5. الوحدة مع أيقونة
            unit = item.unit or "قطعة"
            unit_item = QTableWidgetItem(f"📏 {unit}")
            unit_item.setTextAlignment(Qt.AlignCenter)
            unit_item.setFont(QFont("Segoe UI", 10, QFont.Normal))
            unit_item.setForeground(QColor("#0891b2"))
            unit_item.setToolTip(f"📏 الوحدة: {unit}")
            self.inventory_table.setItem(row, 4, unit_item)

            # 6. الحد الأدنى مع أيقونة
            min_quantity_text = format_quantity(item.min_quantity)
            min_item = QTableWidgetItem(f"📊 {min_quantity_text}")
            min_item.setTextAlignment(Qt.AlignCenter)
            min_item.setFont(QFont("Segoe UI", 10, QFont.Normal))
            min_item.setForeground(QColor("#ea580c"))
            min_item.setToolTip(f"📊 الحد الأدنى: {min_quantity_text}")
            self.inventory_table.setItem(row, 5, min_item)

            # 7. سعر التكلفة مع أيقونة
            cost_price_text = format_currency(item.cost_price)
            cost_item = QTableWidgetItem(f"💰 {cost_price_text}")
            cost_item.setTextAlignment(Qt.AlignCenter)
            cost_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
            cost_item.setForeground(QColor("#dc2626"))
            cost_item.setToolTip(f"💰 سعر التكلفة: {cost_price_text}")
            self.inventory_table.setItem(row, 6, cost_item)

            # 8. سعر البيع مع أيقونة
            selling_price_text = format_currency(item.selling_price)
            selling_item = QTableWidgetItem(f"💵 {selling_price_text}")
            selling_item.setTextAlignment(Qt.AlignCenter)
            selling_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
            selling_item.setForeground(QColor("#059669"))
            selling_item.setToolTip(f"💵 سعر البيع: {selling_price_text}")
            self.inventory_table.setItem(row, 7, selling_item)

            # 9. المورد مع أيقونة
            supplier_name = item.supplier.name if item.supplier else "غير محدد"
            supplier_item = QTableWidgetItem(f"🏢 {supplier_name}")
            supplier_item.setFont(QFont("Segoe UI", 10, QFont.Normal))
            supplier_item.setForeground(QColor("#7c3aed"))
            supplier_item.setToolTip(f"🏢 المورد: {supplier_name}")
            self.inventory_table.setItem(row, 8, supplier_item)

    def update_summary(self, items):
        """تحديث ملخص المخزون"""
        total_items = len(items)
        low_stock_items = sum(1 for item in items if item.quantity <= item.min_quantity)
        total_value = sum(item.quantity * item.cost_price for item in items)

        self.total_label.setText(f"إجمالي العناصر: {total_items} | منخفض المخزون: {low_stock_items} | القيمة: {format_currency(total_value)}")

    def filter_inventory(self):
        """تصفية المخزون بناءً على نص البحث والفئة وحالة المخزون"""
        search_text = self.search_edit.text().strip().lower()
        category = self.category_filter.currentData()
        low_stock = self.low_stock_filter.currentData()

        # بناء الاستعلام
        query = self.session.query(Inventory)

        # تطبيق تصفية النص
        if search_text:
            query = query.filter(
                Inventory.name.like(f"%{search_text}%") |
                Inventory.location.like(f"%{search_text}%")
            )

        # تطبيق تصفية الفئة
        if category:
            query = query.filter(Inventory.category == category)

        # تطبيق تصفية المخزون المنخفض
        if low_stock == "low":
            query = query.filter(Inventory.quantity <= Inventory.min_quantity)

        # تنفيذ الاستعلام
        items = query.order_by(Inventory.name).all()

        # تحديث الجدول والملخص
        self.populate_table(items)
        self.update_summary(items)

    def add_item(self):
        """إضافة عنصر جديد للمخزون"""
        dialog = InventoryItemDialog(self, session=self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # إنشاء عنصر جديد
                item = Inventory(**data)
                self.session.add(item)
                self.session.commit()
                show_info_message("تم", "تم إضافة العنصر بنجاح")
                self.refresh_data()

    def edit_item(self):
        """تعديل عنصر في المخزون"""
        selected_row = self.inventory_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة")
            return

        item_id = int(self.inventory_table.item(selected_row, 0).text())
        item = self.session.query(Inventory).get(item_id)

        if not item:
            show_error_message("خطأ", "لم يتم العثور على العنصر")
            return

        dialog = InventoryItemDialog(self, item, self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # تحديث بيانات العنصر
                for key, value in data.items():
                    setattr(item, key, value)

                self.session.commit()
                show_info_message("تم", "تم تحديث العنصر بنجاح")
                self.refresh_data()

    def delete_item(self):
        """حذف عنصر من المخزون"""
        selected_row = self.inventory_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة")
            return

        item_id = int(self.inventory_table.item(selected_row, 0).text())
        item = self.session.query(Inventory).get(item_id)

        if not item:
            show_error_message("خطأ", "لم يتم العثور على العنصر")
            return

        # طلب تأكيد الحذف
        if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف العنصر '{item.name}'؟"):
            self.session.delete(item)
            self.session.commit()
            show_info_message("تم", "تم حذف العنصر بنجاح")
            self.refresh_data()

    def view_item(self):
        """عرض تفاصيل عنصر المخزون"""
        selected_row = self.inventory_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة")
            return

        item_id = int(self.inventory_table.item(selected_row, 0).text())
        item = self.session.query(Inventory).get(item_id)

        if not item:
            show_error_message("خطأ", "لم يتم العثور على العنصر")
            return

        # إنشاء نافذة لعرض تفاصيل العنصر
        dialog = QDialog(self)
        dialog.setWindowTitle(f"تفاصيل العنصر: {item.name}")
        dialog.setMinimumSize(500, 300)

        layout = QVBoxLayout()

        # عنوان العنصر
        title_label = QLabel(item.name)
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        layout.addWidget(title_label)

        # معلومات العنصر
        info_layout = QFormLayout()

        info_layout.addRow("الفئة:", QLabel(item.category or ""))

        unit_text = f"{format_quantity(item.quantity)} {item.unit}" if item.unit else format_quantity(item.quantity)
        info_layout.addRow("الكمية:", QLabel(unit_text))

        info_layout.addRow("الحد الأدنى:", QLabel(format_quantity(item.min_quantity)))

        info_layout.addRow("سعر التكلفة:", QLabel(format_currency(item.cost_price)))

        info_layout.addRow("سعر البيع:", QLabel(format_currency(item.selling_price)))

        supplier_name = item.supplier.name if item.supplier else ""
        info_layout.addRow("المورد:", QLabel(supplier_name))

        info_layout.addRow("موقع التخزين:", QLabel(item.location or ""))

        last_updated = item.last_updated.strftime("%Y-%m-%d %H:%M") if item.last_updated else ""
        info_layout.addRow("آخر تحديث:", QLabel(last_updated))

        layout.addLayout(info_layout)

        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.accept)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)
        dialog.setLayout(layout)

        dialog.exec_()

    def adjust_quantity(self):
        """تعديل كمية عنصر في المخزون"""
        selected_row = self.inventory_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة")
            return

        item_id = int(self.inventory_table.item(selected_row, 0).text())
        item = self.session.query(Inventory).get(item_id)

        if not item:
            show_error_message("خطأ", "لم يتم العثور على العنصر")
            return

        # إنشاء نافذة لتعديل الكمية
        dialog = QDialog(self)
        dialog.setWindowTitle(f"تعديل كمية العنصر: {item.name}")
        dialog.setMinimumWidth(300)

        layout = QVBoxLayout()

        # معلومات العنصر
        info_label = QLabel(f"العنصر: {item.name}")
        layout.addWidget(info_label)

        current_quantity_label = QLabel(f"الكمية الحالية: {item.quantity} {item.unit}")
        layout.addWidget(current_quantity_label)

        # نموذج تعديل الكمية
        form_layout = QFormLayout()

        # حقل الكمية الجديدة
        self.new_quantity_edit = QDoubleSpinBox()
        self.new_quantity_edit.setRange(0, 100000)
        self.new_quantity_edit.setDecimals(0)  # بدون كسور عشرية
        self.new_quantity_edit.setValue(item.quantity)
        form_layout.addRow("الكمية الجديدة:", self.new_quantity_edit)

        layout.addLayout(form_layout)

        # أزرار الحفظ والإلغاء
        button_layout = QHBoxLayout()

        save_button = QPushButton("حفظ")
        save_button.clicked.connect(lambda: self.save_quantity_adjustment(dialog, item))
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)

        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(dialog.reject)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)

        button_layout.addStretch()
        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)

        layout.addLayout(button_layout)
        dialog.setLayout(layout)

        dialog.exec_()

    def save_quantity_adjustment(self, dialog, item):
        """حفظ تعديل كمية العنصر"""
        new_quantity = self.new_quantity_edit.value()

        # تحديث كمية العنصر
        item.quantity = new_quantity
        item.last_updated = datetime.datetime.now()

        self.session.commit()
        show_info_message("تم", f"تم تحديث كمية العنصر '{item.name}' بنجاح")
        self.refresh_data()
        dialog.accept()

    def export_to_excel(self):
        """تصدير بيانات المخزون إلى Excel"""
        self.export_to_csv()  # نفس الوظيفة

    def export_to_csv(self):
        """تصدير بيانات المخزون إلى CSV"""
        try:
            import csv
            from PyQt5.QtWidgets import QFileDialog

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف CSV", "قائمة_المخزون.csv", "ملفات CSV (*.csv)")
            if not file_path:
                return

            # جمع البيانات من الجدول
            data = []
            headers = []

            # الحصول على عناوين الأعمدة
            for col in range(self.inventory_table.columnCount()):
                headers.append(self.inventory_table.horizontalHeaderItem(col).text())

            # جمع البيانات من الجدول
            for row in range(self.inventory_table.rowCount()):
                row_data = []
                for col in range(self.inventory_table.columnCount()):
                    item = self.inventory_table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)

            # كتابة البيانات إلى ملف CSV
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(headers)
                writer.writerows(data)

            show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def export_to_pdf(self):
        """تصدير بيانات المخزون إلى PDF"""
        try:
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QTextDocument
            from PyQt5.QtWidgets import QFileDialog

            items = self.session.query(Inventory).all()

            if not items:
                show_info_message("تصدير PDF", "لا توجد عناصر للتصدير")
                return

            # حفظ ملف PDF مباشرة
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير المخزون", "تقرير_المخزون.pdf", "PDF Files (*.pdf)"
            )

            if file_path:
                # إنشاء محتوى HTML
                html_content = f"""
                <html dir="rtl">
                <head>
                    <meta charset="utf-8">
                    <title>تقرير المخزون</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 20px; }}
                        h1 {{ color: #6366f1; text-align: center; }}
                        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                        th {{ background-color: #f2f2f2; }}
                    </style>
                </head>
                <body>
                    <h1>📦 تقرير المخزون</h1>
                    <p><strong>تاريخ التقرير:</strong> {QDate.currentDate().toString('yyyy-MM-dd')}</p>

                    <table>
                        <tr>
                            <th>الرقم</th>
                            <th>اسم العنصر</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>القيمة الإجمالية</th>
                            <th>المورد</th>
                        </tr>
                """

                total_value = 0
                for item in items:
                    quantity = item.quantity or 0
                    cost_price = item.cost_price or 0
                    item_total = quantity * cost_price
                    total_value += item_total
                    supplier_name = item.supplier.name if item.supplier else "غير محدد"

                    html_content += f"""
                        <tr>
                            <td>{item.id}</td>
                            <td>{item.name}</td>
                            <td>{int(quantity):,}</td>
                            <td>{int(cost_price):,} جنيه</td>
                            <td>{int(item_total):,} جنيه</td>
                            <td>{supplier_name}</td>
                        </tr>
                    """

                html_content += f"""
                    </table>
                    <h3>إجمالي قيمة المخزون: {int(total_value):,} جنيه</h3>
                </body>
                </html>
                """

                # إنشاء طابعة PDF
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)
                printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

                # إنشاء مستند وطباعته إلى PDF
                document = QTextDocument()
                document.setHtml(html_content)
                document.print_(printer)

                show_info_message("تم", f"تم تصدير المخزون إلى PDF بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تصدير PDF: {str(e)}")

    def export_to_json(self):
        """تصدير بيانات المخزون إلى JSON"""
        try:
            import json
            from PyQt5.QtWidgets import QFileDialog

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف JSON", "قائمة_المخزون.json", "ملفات JSON (*.json)")
            if not file_path:
                return

            # جمع البيانات من الجدول
            data = []
            headers = []

            # الحصول على عناوين الأعمدة
            for col in range(self.inventory_table.columnCount()):
                headers.append(self.inventory_table.horizontalHeaderItem(col).text())

            # جمع البيانات من الجدول
            for row in range(self.inventory_table.rowCount()):
                row_data = {}
                for col in range(self.inventory_table.columnCount()):
                    item = self.inventory_table.item(row, col)
                    row_data[headers[col]] = item.text() if item else ""
                data.append(row_data)

            # كتابة البيانات إلى ملف JSON
            with open(file_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(data, jsonfile, ensure_ascii=False, indent=2)

            show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def view_stock_history(self):
        """عرض تاريخ المخزون للعنصر المحدد"""
        selected_row = self.inventory_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة")
            return

        item_id = int(self.inventory_table.item(selected_row, 0).text())
        item = self.session.query(Inventory).get(item_id)

        if not item:
            show_error_message("خطأ", "لم يتم العثور على العنصر")
            return

        # إنشاء نافذة لعرض تاريخ المخزون
        dialog = QDialog(self)
        dialog.setWindowTitle(f"تاريخ المخزون - {item.name}")
        dialog.setMinimumSize(600, 400)

        layout = QVBoxLayout()

        # معلومات العنصر
        info_text = f"""
📦 تاريخ المخزون - {item.name}

📊 المعلومات الحالية:
• الكمية الحالية: {item.quantity} {item.unit}
• الحد الأدنى: {item.min_quantity} {item.unit}
• سعر التكلفة: {format_currency(item.cost_price)}
• سعر البيع: {format_currency(item.selling_price)}
• آخر تحديث: {item.last_updated.strftime('%Y-%m-%d %H:%M') if item.last_updated else 'غير متوفر'}

📈 الإحصائيات:
• قيمة المخزون: {format_currency(item.quantity * item.cost_price)}
• الربح المتوقع: {format_currency(item.quantity * (item.selling_price - item.cost_price))}
• حالة المخزون: {'منخفض ⚠️' if item.quantity <= item.min_quantity else 'طبيعي ✅'}

📝 ملاحظات:
• يُنصح بإعادة الطلب عند الوصول للحد الأدنى
• تحقق من تواريخ انتهاء الصلاحية إن وجدت
        """

        info_label = QLabel(info_text)
        info_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 12px;")
        layout.addWidget(info_label)

        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.accept)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)
        layout.addWidget(close_button)

        dialog.setLayout(layout)
        dialog.exec_()

    def view_supplier_info(self):
        """عرض معلومات المورد للعنصر المحدد"""
        selected_row = self.inventory_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة")
            return

        item_id = int(self.inventory_table.item(selected_row, 0).text())
        item = self.session.query(Inventory).get(item_id)

        if not item:
            show_error_message("خطأ", "لم يتم العثور على العنصر")
            return

        if not item.supplier:
            show_info_message("معلومات", f"لا يوجد مورد محدد للعنصر '{item.name}'")
            return

        supplier = item.supplier

        # إنشاء نافذة لعرض معلومات المورد
        dialog = QDialog(self)
        dialog.setWindowTitle(f"معلومات المورد - {supplier.name}")
        dialog.setMinimumSize(500, 350)

        layout = QVBoxLayout()

        # معلومات المورد
        supplier_text = f"""
🏪 معلومات المورد

📋 البيانات الأساسية:
• الاسم: {supplier.name}
• الهاتف: {supplier.phone or 'غير متوفر'}
• البريد الإلكتروني: {supplier.email or 'غير متوفر'}
• العنوان: {supplier.address or 'غير متوفر'}

💰 المعلومات المالية:
• الرصيد الحالي: {format_currency(supplier.balance)}
• حالة الرصيد: {'دائن' if supplier.balance > 0 else 'مدين' if supplier.balance < 0 else 'متوازن'}

📦 معلومات العنصر:
• اسم العنصر: {item.name}
• سعر التكلفة: {format_currency(item.cost_price)}
• الكمية المتوفرة: {item.quantity} {item.unit}

📝 ملاحظات:
{supplier.notes or 'لا توجد ملاحظات'}
        """

        supplier_label = QLabel(supplier_text)
        supplier_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 12px;")
        layout.addWidget(supplier_label)

        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.accept)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)
        layout.addWidget(close_button)

        dialog.setLayout(layout)
        dialog.exec_()

    def export_low_stock_report(self):
        """تصدير تقرير المخزون المنخفض"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QTextDocument

            # الحصول على العناصر منخفضة المخزون
            low_stock_items = self.session.query(Inventory).filter(
                Inventory.quantity <= Inventory.min_quantity
            ).all()

            if not low_stock_items:
                show_info_message("معلومات", "لا توجد عناصر منخفضة المخزون حالياً")
                return

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ تقرير المخزون المنخفض", "تقرير_المخزون_المنخفض.pdf", "ملفات PDF (*.pdf)")
            if not file_path:
                return

            # إنشاء طابعة PDF
            printer = QPrinter(QPrinter.HighResolution)
            printer.setOutputFormat(QPrinter.PdfFormat)
            printer.setOutputFileName(file_path)
            printer.setPageSize(QPrinter.A4)
            printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

            # إنشاء مستند نصي
            document = QTextDocument()

            # إنشاء محتوى HTML للتقرير
            html_content = self.generate_low_stock_report_html(low_stock_items)
            document.setHtml(html_content)

            # طباعة المستند إلى PDF
            document.print_(printer)

            show_info_message("تم", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")

    def generate_low_stock_report_html(self, low_stock_items):
        """إنشاء محتوى HTML لتقرير المخزون المنخفض"""
        try:
            html = f"""
            <html dir="rtl">
            <head>
                <meta charset="utf-8">
                <title>تقرير المخزون المنخفض</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ color: #e74c3c; text-align: center; }}
                    h2 {{ color: #34495e; border-bottom: 2px solid #e74c3c; padding-bottom: 5px; }}
                    table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                    th {{ background-color: #e74c3c; color: white; }}
                    .warning {{ background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107; }}
                    .critical {{ background-color: #f8d7da; color: #721c24; }}
                </style>
            </head>
            <body>
                <h1>⚠️ تقرير المخزون المنخفض</h1>
                <p style="text-align: center;">تاريخ التقرير: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M')}</p>

                <div class="warning">
                    <h2>🚨 تحذير</h2>
                    <p>يوجد <strong>{len(low_stock_items)}</strong> عنصر منخفض المخزون يحتاج إلى إعادة طلب فوري!</p>
                </div>

                <h2>📋 تفاصيل العناصر المنخفضة</h2>
                <table>
                    <tr>
                        <th>اسم العنصر</th>
                        <th>الفئة</th>
                        <th>الكمية الحالية</th>
                        <th>الحد الأدنى</th>
                        <th>الوحدة</th>
                        <th>المورد</th>
                        <th>الحالة</th>
                    </tr>
            """

            # إضافة صفوف العناصر
            for item in low_stock_items:
                status_class = "critical" if item.quantity == 0 else ""
                status_text = "نفد المخزون" if item.quantity == 0 else "منخفض"

                html += f"""
                    <tr class="{status_class}">
                        <td>{item.name}</td>
                        <td>{item.category or ''}</td>
                        <td>{item.quantity}</td>
                        <td>{item.min_quantity}</td>
                        <td>{item.unit or ''}</td>
                        <td>{item.supplier.name if item.supplier else 'غير محدد'}</td>
                        <td>{status_text}</td>
                    </tr>
                """

            html += """
                </table>

                <div class="warning">
                    <h2>📝 توصيات</h2>
                    <ul>
                        <li>قم بإعادة طلب العناصر المنخفضة فوراً</li>
                        <li>تواصل مع الموردين لتأكيد توفر العناصر</li>
                        <li>راجع الحد الأدنى للمخزون بانتظام</li>
                        <li>فكر في زيادة الحد الأدنى للعناصر سريعة الاستهلاك</li>
                    </ul>
                </div>
            </body>
            </html>
            """

            return html
        except Exception as e:
            return f"""
            <html dir="rtl">
            <body>
                <h1>خطأ في إنشاء التقرير</h1>
                <p>حدث خطأ أثناء إنشاء تقرير المخزون المنخفض: {str(e)}</p>
            </body>
            </html>
            """

    def create_sample_inventory_data(self):
        """إنشاء بيانات تجريبية للمخزون"""
        try:
            print("🧪 إنشاء بيانات تجريبية للمخزون...")

            sample_items = [
                {
                    'name': 'دهان أبيض داخلي',
                    'category': 'دهانات',
                    'unit': 'علبة',
                    'quantity': 50,
                    'min_quantity': 10,
                    'cost_price': 45,
                    'selling_price': 60,
                    'location': 'مخزن A - رف 1'
                },
                {
                    'name': 'سيراميك أرضي 60x60',
                    'category': 'سيراميك',
                    'unit': 'متر مربع',
                    'quantity': 200,
                    'min_quantity': 50,
                    'cost_price': 25,
                    'selling_price': 35,
                    'location': 'مخزن B - منطقة 2'
                },
                {
                    'name': 'خشب صنوبر 2x4',
                    'category': 'أخشاب',
                    'unit': 'لوح',
                    'quantity': 30,
                    'min_quantity': 5,
                    'cost_price': 80,
                    'selling_price': 120,
                    'location': 'مخزن C - منطقة الأخشاب'
                },
                {
                    'name': 'مغسلة حمام بيضاء',
                    'category': 'أدوات صحية',
                    'unit': 'قطعة',
                    'quantity': 15,
                    'min_quantity': 3,
                    'cost_price': 150,
                    'selling_price': 220,
                    'location': 'مخزن D - الأدوات الصحية'
                },
                {
                    'name': 'كابل كهربائي 2.5 مم',
                    'category': 'أدوات كهربائية',
                    'unit': 'متر',
                    'quantity': 500,
                    'min_quantity': 100,
                    'cost_price': 3,
                    'selling_price': 5,
                    'location': 'مخزن E - الكهربائيات'
                },
                {
                    'name': 'أسمنت بورتلاندي',
                    'category': 'مواد بناء',
                    'unit': 'كيس',
                    'quantity': 100,
                    'min_quantity': 20,
                    'cost_price': 18,
                    'selling_price': 25,
                    'location': 'مخزن F - مواد البناء'
                }
            ]

            for item_data in sample_items:
                inventory_item = Inventory(
                    name=item_data['name'],
                    category=item_data['category'],
                    unit=item_data['unit'],
                    quantity=item_data['quantity'],
                    min_quantity=item_data['min_quantity'],
                    cost_price=item_data['cost_price'],
                    selling_price=item_data['selling_price'],
                    location=item_data['location'],
                    notes=f"عنصر تجريبي - {item_data['category']}"
                )
                self.session.add(inventory_item)

            self.session.commit()
            print(f"✅ تم إنشاء {len(sample_items)} عنصر تجريبي في المخزون")

        except Exception as e:
            print(f"❌ خطأ في إنشاء البيانات التجريبية: {str(e)}")
            self.session.rollback()



    def show_statistics(self):
        """عرض إحصائيات المخزون"""
        try:
            items = self.session.query(Inventory).all()

            if not items:
                show_info_message("إحصائيات المخزون", "لا توجد عناصر لعرض الإحصائيات")
                return

            # حساب الإحصائيات
            total_items = len(items)
            total_quantity = sum(item.quantity or 0 for item in items)
            total_value = sum((item.quantity or 0) * (item.cost_price or 0) for item in items)
            avg_price = sum(item.cost_price or 0 for item in items) / total_items if total_items > 0 else 0

            # إحصائيات المخزون المنخفض
            low_stock_items = [item for item in items if (item.quantity or 0) < (item.min_quantity or 5)]
            low_stock_count = len(low_stock_items)

            # إحصائيات حسب المورد
            supplier_stats = {}
            for item in items:
                supplier_name = item.supplier.name if item.supplier else 'غير محدد'
                if supplier_name in supplier_stats:
                    supplier_stats[supplier_name]['count'] += 1
                    supplier_stats[supplier_name]['value'] += (item.quantity or 0) * (item.cost_price or 0)
                else:
                    supplier_stats[supplier_name] = {
                        'count': 1,
                        'value': (item.quantity or 0) * (item.cost_price or 0)
                    }

            # إحصائيات حسب الفئة
            category_stats = {}
            for item in items:
                category = item.category or 'غير محدد'
                if category in category_stats:
                    category_stats[category] += 1
                else:
                    category_stats[category] = 1

            # إنشاء نافذة الإحصائيات
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextBrowser, QPushButton, QHBoxLayout

            dialog = QDialog(self)
            dialog.setWindowTitle("📊 إحصائيات المخزون")
            dialog.setModal(True)
            dialog.resize(700, 600)

            layout = QVBoxLayout()

            # الإحصائيات العامة
            general_stats = f"""
📊 الإحصائيات العامة:
─────────────────────────────────────────────────────────────────────────────
📦 إجمالي العناصر: {total_items}
📊 إجمالي الكمية: {int(total_quantity):,}
💰 إجمالي القيمة: {int(total_value):,} جنيه
📈 متوسط السعر: {int(avg_price):,} جنيه

⚠️ تنبيهات المخزون:
─────────────────────────────────────────────────────────────────────────────
🔴 عناصر منخفضة المخزون: {low_stock_count} عنصر
"""

            if low_stock_items:
                general_stats += "العناصر المنخفضة:\n"
                for item in low_stock_items[:5]:  # أول 5 عناصر
                    general_stats += f"• {item.name}: {int(item.quantity or 0)} متبقي\n"

            general_stats += f"""
─────────────────────────────────────────────────────────────────────────────

🏪 توزيع حسب المورد:
─────────────────────────────────────────────────────────────────────────────
"""

            # أفضل 5 موردين
            sorted_suppliers = sorted(supplier_stats.items(), key=lambda x: x[1]['value'], reverse=True)[:5]
            for supplier, stats in sorted_suppliers:
                percentage = (stats['count'] / total_items) * 100
                general_stats += f"• {supplier}: {stats['count']} عنصر ({percentage:.1f}%) - {int(stats['value']):,} جنيه\n"

            general_stats += f"""
─────────────────────────────────────────────────────────────────────────────

📂 توزيع حسب الفئة:
─────────────────────────────────────────────────────────────────────────────
"""

            for category, count in category_stats.items():
                percentage = (count / total_items) * 100
                general_stats += f"• {category}: {count} عنصر ({percentage:.1f}%)\n"

            # عرض الإحصائيات
            stats_text = QTextBrowser()
            stats_text.setPlainText(general_stats)
            stats_text.setStyleSheet("""
                QTextBrowser {
                    font-family: 'Segoe UI', Arial, sans-serif;
                    font-size: 11px;
                    line-height: 1.4;
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 15px;
                }
            """)
            layout.addWidget(stats_text)

            # أزرار الإجراءات
            buttons_layout = QHBoxLayout()

            export_stats_btn = QPushButton("📤 تصدير الإحصائيات")
            export_stats_btn.clicked.connect(lambda: self.export_statistics_report(general_stats))
            buttons_layout.addWidget(export_stats_btn)

            low_stock_btn = QPushButton("⚠️ تقرير المخزون المنخفض")
            low_stock_btn.clicked.connect(lambda: self.export_low_stock_report())
            buttons_layout.addWidget(low_stock_btn)

            close_button = QPushButton("❌ إغلاق")
            close_button.clicked.connect(dialog.close)
            buttons_layout.addWidget(close_button)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في عرض الإحصائيات: {str(e)}")

    def export_statistics_report(self, stats_content):
        """تصدير تقرير الإحصائيات"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الإحصائيات", "إحصائيات_المخزون.txt", "Text Files (*.txt)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"""
═══════════════════════════════════════════════════════════════════════════════
                            📊 تقرير إحصائيات المخزون
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الإنشاء: {QDate.currentDate().toString('hh:mm:ss')}

{stats_content}

═══════════════════════════════════════════════════════════════════════════════
                        تم إنشاء التقرير بواسطة نظام إدارة المخزون
═══════════════════════════════════════════════════════════════════════════════
""")

                show_info_message("تم", f"تم تصدير تقرير الإحصائيات بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تصدير التقرير: {str(e)}")

    def export_low_stock_report(self):
        """تصدير تقرير المخزون المنخفض"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            items = self.session.query(Inventory).all()
            low_stock_items = [item for item in items if (item.quantity or 0) < (item.min_quantity or 5)]

            if not low_stock_items:
                show_info_message("تقرير المخزون المنخفض", "لا توجد عناصر منخفضة المخزون")
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير المخزون المنخفض", "المخزون_المنخفض.txt", "Text Files (*.txt)"
            )

            if file_path:
                report_content = f"""
═══════════════════════════════════════════════════════════════════════════════
                            ⚠️ تقرير المخزون المنخفض
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الإنشاء: {QDate.currentDate().toString('hh:mm:ss')}

🔴 عدد العناصر المنخفضة: {len(low_stock_items)}

📋 تفاصيل العناصر المنخفضة:
─────────────────────────────────────────────────────────────────────────────
"""

                for item in low_stock_items:
                    supplier_name = item.supplier.name if item.supplier else "غير محدد"
                    report_content += f"""
🔸 {item.name}
   📊 الكمية الحالية: {int(item.quantity or 0)}
   ⚠️ الحد الأدنى: {int(item.min_quantity or 5)}
   💰 سعر التكلفة: {int(item.cost_price or 0):,} جنيه
   🏪 المورد: {supplier_name}
   📂 الفئة: {item.category or 'غير محدد'}
   ─────────────────────────────────────────────────────────────────────────────
"""

                report_content += """
💡 التوصيات:
─────────────────────────────────────────────────────────────────────────────
• إعادة طلب العناصر المنخفضة فوراً
• مراجعة الحد الأدنى للمخزون
• التواصل مع الموردين لتأكيد التوفر
• مراقبة معدل الاستهلاك

═══════════════════════════════════════════════════════════════════════════════
                        تم إنشاء التقرير بواسطة نظام إدارة المخزون
═══════════════════════════════════════════════════════════════════════════════
"""

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(report_content)

                show_info_message("تم", f"تم تصدير تقرير المخزون المنخفض بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تصدير التقرير: {str(e)}")

    def view_stock_alerts(self):
        """عرض تنبيهات المخزون"""
        try:
            items = self.session.query(Inventory).all()
            low_stock_items = [item for item in items if (item.quantity or 0) < (item.min_quantity or 5)]

            # إنشاء نافذة التنبيهات
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QListWidget, QListWidgetItem, QPushButton, QHBoxLayout

            dialog = QDialog(self)
            dialog.setWindowTitle("⚠️ تنبيهات المخزون")
            dialog.setModal(True)
            dialog.resize(600, 400)

            layout = QVBoxLayout()

            # معلومات التنبيهات
            info_label = QLabel(f"🔴 عدد العناصر المنخفضة: {len(low_stock_items)}")
            info_label.setStyleSheet("font-weight: bold; font-size: 14px; padding: 10px; background-color: #f8d7da; border-radius: 5px; color: #721c24;")
            layout.addWidget(info_label)

            # قائمة التنبيهات
            alerts_list = QListWidget()
            alerts_list.setStyleSheet("""
                QListWidget {
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    padding: 5px;
                }
                QListWidget::item {
                    padding: 10px;
                    border-bottom: 1px solid #eee;
                    margin: 2px;
                    border-radius: 3px;
                }
                QListWidget::item:selected {
                    background-color: #fff3cd;
                }
            """)

            if low_stock_items:
                for item in low_stock_items:
                    alert_text = f"⚠️ {item.name} - الكمية: {int(item.quantity or 0)} (الحد الأدنى: {int(item.min_quantity or 5)})"
                    list_item = QListWidgetItem(alert_text)
                    alerts_list.addItem(list_item)
            else:
                no_alerts_item = QListWidgetItem("✅ لا توجد تنبيهات - جميع العناصر فوق الحد الأدنى")
                alerts_list.addItem(no_alerts_item)

            layout.addWidget(alerts_list)

            # أزرار الإجراءات
            buttons_layout = QHBoxLayout()

            if low_stock_items:
                export_btn = QPushButton("📤 تصدير التقرير")
                export_btn.clicked.connect(lambda: (dialog.close(), self.export_low_stock_report()))
                buttons_layout.addWidget(export_btn)

            close_btn = QPushButton("❌ إغلاق")
            close_btn.clicked.connect(dialog.close)
            buttons_layout.addWidget(close_btn)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في عرض التنبيهات: {str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة مطابق للفواتير"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق للفواتير
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#047857', 'hover_mid': '#059669', 'hover_end': '#10b981', 'hover_bottom': '#34d399',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#052e16', 'pressed_border': '#064e3b',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.5)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#991b1b', 'hover_mid': '#dc2626', 'hover_end': '#ef4444', 'hover_bottom': '#f87171',
                    'hover_border': '#ef4444', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.5)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0284c7', 'bg_bottom': '#0ea5e9',
                    'hover_start': '#075985', 'hover_mid': '#0891b2', 'hover_end': '#0ea5e9', 'hover_bottom': '#38bdf8',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0284c7', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.5)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#14b8a6',
                    'hover_start': '#134e4a', 'hover_mid': '#0d9488', 'hover_end': '#14b8a6', 'hover_bottom': '#2dd4bf',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.5)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#06b6d4',
                    'hover_start': '#164e63', 'hover_mid': '#0891b2', 'hover_end': '#06b6d4', 'hover_bottom': '#22d3ee',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.5)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#ec4899',
                    'hover_start': '#831843', 'hover_mid': '#be185d', 'hover_end': '#ec4899', 'hover_bottom': '#f472b6',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.5)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#6366f1',
                    'hover_start': '#312e81', 'hover_mid': '#4f46e5', 'hover_end': '#6366f1', 'hover_bottom': '#818cf8',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4f46e5', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.5)'
                },
                'orange': {
                    'bg_start': '#431407', 'bg_mid': '#7c2d12', 'bg_end': '#9a3412', 'bg_bottom': '#f97316',
                    'hover_start': '#7c2d12', 'hover_mid': '#c2410c', 'hover_end': '#f97316', 'hover_bottom': '#fb923c',
                    'hover_border': '#f97316', 'pressed_start': '#431407', 'pressed_mid': '#7c2d12',
                    'pressed_end': '#9a3412', 'pressed_bottom': '#c2410c', 'pressed_border': '#9a3412',
                    'border': '#f97316', 'text': '#ffffff', 'shadow': 'rgba(249, 115, 22, 0.5)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات - مطابق للفواتير
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']},
                               0 0 30px rgba(255, 255, 255, 0.1);
                    letter-spacing: 0.3px;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 4px solid {color_scheme['hover_border']};
                    transform: translateY(-2px);
                    box-shadow: 0 8px 20px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 25px {color_scheme['shadow']},
                               0 0 40px rgba(255, 255, 255, 0.15);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px);
                    box-shadow: 0 3px 8px {color_scheme['shadow']},
                               inset 0 1px 0 rgba(255, 255, 255, 0.2),
                               inset 0 -1px 0 rgba(0, 0, 0, 0.5),
                               0 0 15px {color_scheme['shadow']};
                }}
                QPushButton::menu-indicator {{
                    {f"image: none; width: 0px;" if not has_menu else "width: 12px; height: 12px; margin-right: 4px;"}
                }}
            """

            button.setStyleSheet(style)
            print(f"✅ تم تطبيق التصميم المتطور على الزر: {button.text()} - نوع: {button_type}")

        except Exception as e:
            print(f"❌ خطأ في تطبيق التصميم على الزر: {str(e)}")



    def create_sample_data(self):
        """إنشاء بيانات تجريبية بسيطة للمخزون"""
        try:
            # إضافة صفوف تجريبية للجدول
            sample_data = [
                ["1", "دهان أبيض", "دهانات", "100", "50", "25.00", "30.00", "المخزن الرئيسي", "متوفر"],
                ["2", "سيراميك أرضي", "سيراميك", "200", "150", "15.00", "20.00", "المخزن الفرعي", "متوفر"],
                ["3", "خشب صنوبر", "أخشاب", "50", "30", "100.00", "120.00", "المخزن الرئيسي", "منخفض"],
                ["4", "مفتاح كهربائي", "أدوات كهربائية", "500", "400", "5.00", "8.00", "المخزن الفرعي", "متوفر"],
                ["5", "حنفية مياه", "أدوات صحية", "75", "60", "45.00", "55.00", "المخزن الرئيسي", "متوفر"]
            ]

            self.inventory_table.setRowCount(len(sample_data))

            for row, data in enumerate(sample_data):
                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))
                    item.setFont(QFont("Arial", 10, QFont.Bold))
                    item.setForeground(QColor("#000000"))

                    # تلوين حسب الحالة
                    if col == 8:  # عمود الحالة
                        if value == "منخفض":
                            item.setForeground(QColor("#dc2626"))
                        elif value == "متوفر":
                            item.setForeground(QColor("#059669"))

                    self.inventory_table.setItem(row, col, item)

            # تحديث الملخص
            self.total_label.setText("إجمالي العناصر: 5 | منخفض المخزون: 1 | القيمة: 12,500.00 ج.م")

            print("✅ تم إنشاء بيانات تجريبية للمخزون")

        except Exception as e:
            print(f"❌ خطأ في إنشاء البيانات التجريبية: {str(e)}")
            # إنشاء جدول فارغ على الأقل
            self.inventory_table.setRowCount(0)
            self.total_label.setText("إجمالي العناصر: 0")
