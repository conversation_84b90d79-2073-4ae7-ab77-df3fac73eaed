from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QHeaderView, QMessageBox, QDialog, QTextEdit, QComboBox,
                            QTabWidget, QSizePolicy, QFrame)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QIcon, QFont, QColor

from database import Notification, Invoice, Client, Reminder
from utils import show_error_message, show_info_message, show_confirmation_message, format_datetime
import datetime

from ui.unified_styles import (UnifiedStyles, StyledButton, StyledGroupBox,
                                StyledTable, StyledLabel, StyledTabWidget)

class NotificationsWidget(QWidget):
    """واجهة إدارة الإشعارات والتنبيهات"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()
        self.refresh_data()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مع تقليل المساحات الفارغة لاستغلال المساحة للجدول
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # هوامش مصغرة جداً
        main_layout.setSpacing(2)  # مسافات مصغرة

        # إنشاء تبويبات للإشعارات والتنبيهات مع تحسين العرض
        styled_tabs = StyledTabWidget()
        self.tabs = styled_tabs.tab_widget

        # تحسين تصميم التبويبات مع إطار أسود وعرض أكبر 3 أضعاف
        self.tabs.setStyleSheet("""
            QTabWidget {
                border: 3px solid #000000;
                border-radius: 8px;
                background-color: #f8fafc;
                font-size: 14px;
                font-weight: bold;
            }
            QTabWidget::pane {
                border: 3px solid #000000;
                border-radius: 8px;
                background-color: #ffffff;
                top: -3px;
            }
            QTabBar {
                alignment: center;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e2e8f0,
                    stop:0.5 #cbd5e1,
                    stop:1 #94a3b8);
                color: #1e293b;
                border: 3px solid #000000;
                border-bottom: none;
                border-radius: 8px 8px 0 0;
                padding: 8px 32px;
                margin: 2px;
                font-size: 14px;
                font-weight: bold;
                min-width: 800px;
                max-width: 1200px;
                height: 35px;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3b82f6,
                    stop:0.5 #2563eb,
                    stop:1 #1d4ed8);
                color: #ffffff;
                border: 3px solid #000000;
                border-bottom: 3px solid #ffffff;
                margin-bottom: -3px;
                height: 35px;
            }
            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f1f5f9,
                    stop:0.5 #e2e8f0,
                    stop:1 #cbd5e1);
                border: 3px solid #000000;
            }
        """)

        # إنشاء تبويب الإشعارات
        self.notifications_tab = QWidget()
        self.init_notifications_tab()

        # إضافة تبويب الإشعارات إلى التبويبات
        self.tabs.addTab(self.notifications_tab, "🔔 الإشعارات")

        # إنشاء تبويب التنبيهات
        from ui.reminders import RemindersWidget
        self.reminders_widget = RemindersWidget(self.session)
        self.tabs.addTab(self.reminders_widget, "⏰ التنبيهات")

        # تعيين سياسة الحجم للتبويبات لتأخذ العرض الكامل
        self.tabs.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # إضافة التبويبات إلى التخطيط الرئيسي
        main_layout.addWidget(self.tabs)

        self.setLayout(main_layout)

    def init_notifications_tab(self):
        """تهيئة تبويب الإشعارات"""
        # إنشاء التخطيط الرئيسي لتبويب الإشعارات مع تقليل المساحات الفارغة لاستغلال المساحة للجدول
        notifications_layout = QVBoxLayout(self.notifications_tab)
        notifications_layout.setContentsMargins(1, 1, 1, 1)  # هوامش مصغرة جداً لاستغلال المساحة
        notifications_layout.setSpacing(2)  # مسافات مصغرة

        # تعيين سياسة الحجم للتبويب ليأخذ العرض الكامل
        self.notifications_tab.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للفواتير
        title_label = QLabel("🔔 إدارة الإشعارات المتطورة - نظام شامل ومتقدم لإدارة الإشعارات مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        notifications_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن مع إطار أسود
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e1);
                border: 3px solid #000000;
                border-radius: 8px;
                margin: 1px;
                padding: 2px;
                max-height: 55px;
                min-height: 50px;
            }
        """)
        # تعيين سياسة الحجم للإطار العلوي
        top_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        # تخطيط أفقي محسن مع مساحة أكبر للعناصر
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(5, 5, 5, 5)  # هوامش صغيرة
        search_layout.setSpacing(6)  # مسافات مناسبة بين العناصر

        # إنشاء حاوي عمودي للتوسيط مع تحسين المساحة
        top_container = QVBoxLayout()
        top_container.setContentsMargins(3, 3, 3, 3)  # هوامش صغيرة
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط (أقل)
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط (أقل)
        top_container.addStretch(1)

        # تسمية البحث محسنة مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث بالعنوان أو الرسالة...")
        self.search_edit.textChanged.connect(self.filter_notifications)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 3px solid rgba(139, 92, 246, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(240, 249, 255, 0.9),
                    stop:1 rgba(224, 242, 254, 0.85));
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QLineEdit:hover {
                border: 3px solid rgba(124, 58, 237, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
            }
        """)
        # تعيين سياسة الحجم لحقل البحث ليأخذ المساحة المتاحة
        self.search_edit.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px;
                font-size: 20px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(91, 33, 182, 0.9),
                    stop:1 rgba(76, 29, 149, 0.8));
                border: 3px solid rgba(91, 33, 182, 0.9);
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
            }
        """)
        search_button.clicked.connect(self.filter_notifications)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية النوع محسنة مطابقة للفواتير
        type_label = QLabel("📋 نوع:")
        type_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        type_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إضافة حقل تصفية حسب النوع محسن
        self.type_filter = QComboBox()
        self.type_filter.addItem("جميع الأنواع", None)
        self.type_filter.addItem("فواتير مستحقة", "invoice_due")
        self.type_filter.addItem("تنبيهات مستحقة", "reminder_due")
        self.type_filter.currentIndexChanged.connect(self.filter_notifications)
        self.type_filter.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                min-width: 120px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QComboBox:hover {
                border: 3px solid rgba(124, 58, 237, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
            }
            QComboBox:focus {
                border: 3px solid rgba(139, 92, 246, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(240, 249, 255, 0.9),
                    stop:1 rgba(224, 242, 254, 0.85));
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid rgba(96, 165, 250, 0.8);
                width: 8px;
                height: 8px;
                border-radius: 4px;
                background: rgba(96, 165, 250, 0.8);
            }
        """)
        self.type_filter.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)

        # تسمية الحالة محسنة مطابقة للفواتير
        status_label = QLabel("🎯 حالة:")
        status_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        status_label.setAlignment(Qt.AlignCenter)

        # إضافة حقل تصفية حسب الحالة محسن
        self.read_filter = QComboBox()
        self.read_filter.addItem("الكل", None)
        self.read_filter.addItem("غير مقروءة", False)
        self.read_filter.addItem("مقروءة", True)
        self.read_filter.currentIndexChanged.connect(self.filter_notifications)
        self.read_filter.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                min-width: 120px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QComboBox:hover {
                border: 3px solid rgba(124, 58, 237, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
            }
            QComboBox:focus {
                border: 3px solid rgba(139, 92, 246, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(240, 249, 255, 0.9),
                    stop:1 rgba(224, 242, 254, 0.85));
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid rgba(96, 165, 250, 0.8);
                width: 8px;
                height: 8px;
                border-radius: 4px;
                background: rgba(96, 165, 250, 0.8);
            }
        """)
        self.read_filter.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 3, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(type_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.type_filter, 1, Qt.AlignVCenter)
        search_layout.addWidget(status_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.read_filter, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء جدول الإشعارات المتطور والمحسن
        self.create_advanced_notifications_table()

        notifications_layout.addWidget(top_frame)
        notifications_layout.addWidget(self.notifications_table, 1)  # إعطاء الجدول أولوية في التمدد

        # تعيين سياسة الحجم للجدول ليأخذ العرض والارتفاع المتاح
        self.notifications_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # إنشاء إطار سفلي للأزرار مع إطار أسود
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e1);
                border: 3px solid #000000;
                border-radius: 8px;
                margin: 1px;
                padding: 2px;
                max-height: 60px;
                min-height: 55px;
            }
        """)
        # تعيين سياسة الحجم للإطار السفلي
        bottom_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار بأحجام محسنة وأكثر إحكاماً

        # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة
        self.view_button = QPushButton("👁️ عرض التفاصيل")
        self.style_advanced_button(self.view_button, 'indigo', has_menu=True)  # بنفسجي للتفاصيل مع قائمة
        self.view_button.clicked.connect(self.view_notification)
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.mark_read_button = QPushButton("✅ تعليم كمقروء")
        self.style_advanced_button(self.mark_read_button, 'emerald')  # أخضر للقراءة
        self.mark_read_button.clicked.connect(self.mark_as_read)
        self.mark_read_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.mark_unread_button = QPushButton("❌ تعليم كغير مقروء")
        self.style_advanced_button(self.mark_unread_button, 'orange')  # برتقالي لعدم القراءة
        self.mark_unread_button.clicked.connect(self.mark_as_unread)
        self.mark_unread_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')  # أحمر تحذيري
        self.delete_button.clicked.connect(self.delete_notification)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الثانية - العمليات المتقدمة
        self.check_due_invoices_button = QPushButton("📋 فواتير مستحقة")
        self.style_advanced_button(self.check_due_invoices_button, 'info')  # أزرق للفواتير
        self.check_due_invoices_button.clicked.connect(self.check_due_invoices)
        self.check_due_invoices_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.check_due_reminders_button = QPushButton("⏰ تنبيهات مستحقة")
        self.style_advanced_button(self.check_due_reminders_button, 'cyan')  # سماوي للتنبيهات
        self.check_due_reminders_button.clicked.connect(self.check_due_reminders)
        self.check_due_reminders_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # تصميم حديث ومتطور
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)



        # إجمالي الإشعارات مطور ليتشابه مع الفواتير
        self.total_label = QLabel("إجمالي الإشعارات: 0")
        self.total_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                padding: 8px 16px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #064e3b,
                    stop:0.1 #047857,
                    stop:0.9 #065f46,
                    stop:1 #10b981);
                border: 5px solid #10b981;
                border-radius: 20px;
                min-height: 34px;
                max-height: 38px;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                           2px 2px 4px rgba(0, 0, 0, 0.7),
                           1px 1px 2px rgba(0, 0, 0, 0.5);
                box-shadow: 0 8px 20px rgba(16, 185, 129, 0.6),
                           inset 0 3px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(16, 185, 129, 0.6),
                           0 0 40px rgba(255, 255, 255, 0.1);
                letter-spacing: 0.5px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        """)
        self.total_label.setAlignment(Qt.AlignCenter)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.mark_read_button)
        actions_layout.addWidget(self.mark_unread_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.check_due_invoices_button)
        actions_layout.addWidget(self.check_due_reminders_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.total_label)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        # تجميع التخطيط النهائي
        notifications_layout.addWidget(bottom_frame)

    def create_advanced_notifications_table(self):
        """إنشاء جدول الإشعارات المتطور والمحسن مطابق للموردين"""
        styled_table = StyledTable()
        self.notifications_table = styled_table.table
        self.notifications_table.setColumnCount(5)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "🆔 الرقم التسلسلي",
            "📢 العنوان",
            "📅 التاريخ",
            "🏷️ النوع",
            "🎯 الحالة"
        ]
        self.notifications_table.setHorizontalHeaderLabels(headers)

        # تحسين عرض الأعمدة
        header = self.notifications_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # الرقم
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # العنوان
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # التاريخ
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # النوع
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # الحالة

        # تحديد عرض الأعمدة الثابتة
        self.notifications_table.setColumnWidth(0, 80)   # الرقم
        self.notifications_table.setColumnWidth(2, 150)  # التاريخ
        self.notifications_table.setColumnWidth(3, 150)  # النوع
        self.notifications_table.setColumnWidth(4, 120)  # الحالة

        self.notifications_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.notifications_table.setSelectionMode(QTableWidget.SingleSelection)
        self.notifications_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.notifications_table.setAlternatingRowColors(True)
        self.notifications_table.doubleClicked.connect(self.view_notification)

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للمصروفات والإيرادات والفواتير
        self.notifications_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للمصروفات والإيرادات والفواتير
        header = self.notifications_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                vertical-align: middle !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للمصروفات والإيرادات والفواتير
        self.notifications_table.verticalHeader().setDefaultSectionSize(45)
        self.notifications_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        # إضافة العلامة المائية للجدول مطابقة للفواتير
        self.add_watermark_to_notifications_table()

    def add_watermark_to_notifications_table(self):
        """إضافة علامة مائية لجدول الإشعارات مطابقة للفواتير"""
        try:
            # إنشاء العلامة المائية مثل الفواتير
            watermark = QLabel("Smart Finish", self.notifications_table)
            watermark.setAlignment(Qt.AlignCenter)

            # تنسيق العلامة المائية مكبرة أكثر للتوحيد مع الباقي
            font = QFont("Arial", 150, QFont.Bold)
            watermark.setFont(font)

            # تطبيق تصميم محسن للعلامة المائية - أكثر وضوحاً لكن لا يؤثر على البيانات
            watermark.setStyleSheet("""
                QLabel {
                    color: rgba(71, 85, 105, 0.35);
                    background: transparent;
                    border: none;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
                    font-weight: bold;
                }
            """)

            # إصلاح مشكلة التفاعل - العلامة المائية لا تتداخل مع النقرات
            watermark.setAttribute(Qt.WA_TransparentForMouseEvents, True)
            watermark.setEnabled(False)  # تعطيل التفاعل مع العلامة المائية
            watermark.lower()  # وضع العلامة المائية في الخلف

            # تحسين إضافي للشفافية والوضوح
            watermark.setWindowOpacity(0.6)  # شفافية محسنة

            # تحديد موضع وحجم العلامة المائية مع تحسين الموضع
            def update_watermark_geometry():
                if self.notifications_table.isVisible():
                    rect = self.notifications_table.rect()
                    # تحسين الموضع ليكون في المنتصف مع مساحة أفضل
                    watermark.setGeometry(rect.x() + 50, rect.y() + 100,
                                        rect.width() - 100, rect.height() - 200)
                    watermark.lower()  # وضع العلامة المائية في الخلف دائماً
                    watermark.setAttribute(Qt.WA_TransparentForMouseEvents, True)

            # ربط تحديث الموضع بتغيير حجم الجدول
            original_resize_event = self.notifications_table.resizeEvent
            def custom_resize_event(event):
                original_resize_event(event)
                update_watermark_geometry()
            self.notifications_table.resizeEvent = custom_resize_event

            # تحديث أولي للموضع
            update_watermark_geometry()

        except Exception as e:
            print(f"خطأ في إضافة العلامة المائية للإشعارات: {str(e)}")

    def refresh_data(self):
        """تحديث بيانات الإشعارات في الجدول"""
        # الحصول على جميع الإشعارات من قاعدة البيانات
        notifications = self.session.query(Notification).order_by(Notification.date.desc()).all()
        self.populate_table(notifications)
        self.update_summary(notifications)

    def populate_table(self, notifications):
        """ملء جدول الإشعارات بالبيانات"""
        self.notifications_table.setRowCount(0)

        for row, notification in enumerate(notifications):
            self.notifications_table.insertRow(row)
            # 1. الرقم مع أيقونة
            id_item = QTableWidgetItem(f"🔢 {notification.id}")
            id_item.setTextAlignment(Qt.AlignCenter)
            id_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
            id_item.setForeground(QColor("#1e40af"))
            id_item.setToolTip(f"🔢 رقم الإشعار: {notification.id}")
            self.notifications_table.setItem(row, 0, id_item)

            # 2. العنوان مع أيقونة
            title_item = QTableWidgetItem(f"📢 {notification.title}")
            title_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
            title_item.setForeground(QColor("#059669"))
            title_item.setToolTip(f"📢 عنوان الإشعار: {notification.title}")
            self.notifications_table.setItem(row, 1, title_item)

            # 3. التاريخ مع أيقونة
            date = format_datetime(notification.date) if notification.date else "غير محدد"
            date_item = QTableWidgetItem(f"📅 {date}")
            date_item.setTextAlignment(Qt.AlignCenter)
            date_item.setFont(QFont("Segoe UI", 10, QFont.Normal))
            date_item.setForeground(QColor("#0891b2"))
            date_item.setToolTip(f"📅 تاريخ الإشعار: {date}")
            self.notifications_table.setItem(row, 2, date_item)

            # 4. نوع الإشعار مع أيقونات مناسبة
            type_icons = {
                'invoice_due': '💰',
                'reminder_due': '⏰',
                'payment_received': '✅',
                'low_stock': '📦',
                'project_update': '🏗️'
            }
            type_map = {
                'invoice_due': 'فاتورة مستحقة',
                'reminder_due': 'تنبيه مستحق',
                'payment_received': 'دفعة مستلمة',
                'low_stock': 'مخزون منخفض',
                'project_update': 'تحديث مشروع'
            }
            type_icon = type_icons.get(notification.type, '📋')
            type_text = type_map.get(notification.type, notification.type or "غير محدد")
            type_item = QTableWidgetItem(f"{type_icon} {type_text}")
            type_item.setTextAlignment(Qt.AlignCenter)
            type_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
            type_item.setForeground(QColor("#7c3aed"))
            type_item.setToolTip(f"{type_icon} نوع الإشعار: {type_text}")
            self.notifications_table.setItem(row, 3, type_item)

            # 5. حالة الإشعار مع أيقونات ملونة
            if notification.is_read:
                status_item = QTableWidgetItem("✅ مقروء")
                status_item.setBackground(QColor(200, 255, 200))
                status_item.setForeground(QColor("#059669"))
                status_item.setToolTip("✅ تم قراءة هذا الإشعار")
            else:
                status_item = QTableWidgetItem("🔔 غير مقروء")
                status_item.setBackground(QColor(255, 200, 200))
                status_item.setForeground(QColor("#dc2626"))
                status_item.setToolTip("🔔 إشعار جديد لم يتم قراءته")

                # تلوين الصف كاملاً للإشعارات غير المقروءة
                for col in range(5):
                    item = self.notifications_table.item(row, col)
                    if item:
                        item.setBackground(QColor(255, 245, 245))

            status_item.setTextAlignment(Qt.AlignCenter)
            status_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
            self.notifications_table.setItem(row, 4, status_item)

    def update_summary(self, notifications):
        """تحديث ملخص الإشعارات"""
        total = len(notifications)
        unread = sum(1 for notification in notifications if not notification.is_read)

        self.total_label.setText(f"إجمالي الإشعارات: {total} | غير المقروءة: {unread}")

    def filter_notifications(self):
        """تصفية الإشعارات بناءً على نص البحث والنوع والحالة"""
        search_text = self.search_edit.text().strip().lower()
        notification_type = self.type_filter.currentData()
        is_read = self.read_filter.currentData()

        # بناء الاستعلام
        query = self.session.query(Notification)

        # تطبيق تصفية النص
        if search_text:
            query = query.filter(
                Notification.title.like(f"%{search_text}%") |
                Notification.message.like(f"%{search_text}%")
            )

        # تطبيق تصفية النوع
        if notification_type:
            query = query.filter(Notification.type == notification_type)

        # تطبيق تصفية الحالة
        if is_read is not None:
            query = query.filter(Notification.is_read == is_read)

        # تنفيذ الاستعلام
        notifications = query.order_by(Notification.date.desc()).all()

        # تحديث الجدول والملخص
        self.populate_table(notifications)
        self.update_summary(notifications)

    def view_notification(self):
        """عرض تفاصيل الإشعار"""
        selected_row = self.notifications_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار إشعار من القائمة")
            return

        notification_id = int(self.notifications_table.item(selected_row, 0).text())
        notification = self.session.query(Notification).get(notification_id)

        if not notification:
            show_error_message("خطأ", "لم يتم العثور على الإشعار")
            return

        # تعليم الإشعار كمقروء
        if not notification.is_read:
            notification.is_read = True
            self.session.commit()

        # إنشاء نافذة لعرض تفاصيل الإشعار
        dialog = QDialog(self)
        dialog.setWindowTitle(notification.title)
        dialog.setMinimumSize(500, 300)

        layout = QVBoxLayout()

        # عنوان الإشعار
        title_label = QLabel(notification.title)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(title_label)

        # تاريخ الإشعار
        date_str = format_datetime(notification.date) if notification.date else ""
        date_label = QLabel(f"التاريخ: {date_str}")
        layout.addWidget(date_label)

        # نص الإشعار
        message_text = QTextEdit()
        message_text.setReadOnly(True)
        message_text.setText(notification.message)
        layout.addWidget(message_text)

        # إذا كان الإشعار متعلق بفاتورة، عرض معلومات الفاتورة
        if notification.type == 'invoice_due' and notification.related_id:
            invoice = self.session.query(Invoice).get(notification.related_id)
            if invoice:
                invoice_info = QLabel(f"الفاتورة: {invoice.invoice_number}")
                layout.addWidget(invoice_info)

                client_name = invoice.client.name if invoice.client else "غير محدد"
                client_info = QLabel(f"العميل: {client_name}")
                layout.addWidget(client_info)

                amount_info = QLabel(f"المبلغ المستحق: {invoice.total_amount - invoice.paid_amount:.2f}")
                layout.addWidget(amount_info)

                # زر للانتقال إلى الفاتورة
                goto_invoice_button = StyledButton("الانتقال إلى الفاتورة", "primary", "normal")
                goto_invoice_button.clicked.connect(lambda: self.goto_invoice(invoice.id))
                layout.addWidget(goto_invoice_button.button)

        # إذا كان الإشعار متعلق بتنبيه، عرض معلومات التنبيه
        elif notification.type == 'reminder_due' and notification.related_id:
            reminder = self.session.query(Reminder).get(notification.related_id)
            if reminder:
                reminder_title = QLabel(f"<b>عنوان التنبيه:</b> {reminder.title}")
                layout.addWidget(reminder_title)

                # تاريخ التنبيه
                date_str = format_datetime(reminder.reminder_date) if reminder.reminder_date else ""
                date_label = QLabel(f"<b>تاريخ التنبيه:</b> {date_str}")
                layout.addWidget(date_label)

                # الأولوية
                priority_map = {
                    'high': 'عالية',
                    'medium': 'متوسطة',
                    'low': 'منخفضة'
                }
                priority_text = priority_map.get(reminder.priority, reminder.priority or "")
                priority_label = QLabel(f"<b>الأولوية:</b> {priority_text}")
                layout.addWidget(priority_label)

                # الحالة
                status_text = "مكتمل" if reminder.is_completed else "غير مكتمل"
                status_label = QLabel(f"<b>الحالة:</b> {status_text}")
                layout.addWidget(status_label)

                # الوصف
                if reminder.description:
                    description_label = QLabel("<b>الوصف:</b>")
                    layout.addWidget(description_label)

                    description_text = QTextEdit()
                    description_text.setReadOnly(True)
                    description_text.setText(reminder.description)
                    description_text.setMaximumHeight(100)
                    layout.addWidget(description_text)

                # زر للانتقال إلى التنبيه
                goto_reminder_button = StyledButton("الانتقال إلى التنبيهات", "primary", "normal")
                goto_reminder_button.clicked.connect(lambda: self.goto_reminder(reminder.id))
                layout.addWidget(goto_reminder_button.button)

        # زر إغلاق
        close_button = StyledButton("إغلاق", "secondary", "normal")
        close_button.clicked.connect(dialog.accept)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_button.button)

        layout.addLayout(button_layout)
        dialog.setLayout(layout)

        dialog.exec_()

        # تحديث الجدول بعد عرض الإشعار
        self.refresh_data()

    def goto_invoice(self, invoice_id):
        """الانتقال إلى الفاتورة المرتبطة بالإشعار"""
        # هذه الوظيفة يجب أن تكون مرتبطة بواجهة الفواتير
        # يمكن تنفيذها لاحقًا عند ربط جميع الواجهات معًا
        show_info_message("معلومات", f"سيتم الانتقال إلى الفاتورة رقم {invoice_id}")

    def goto_reminder(self, reminder_id):
        """الانتقال إلى التنبيه المرتبط بالإشعار"""
        # التبديل إلى تبويب التنبيهات
        if hasattr(self, 'tabs') and hasattr(self, 'reminders_widget'):
            self.tabs.setCurrentWidget(self.reminders_widget)
            # تحديد التنبيه في قائمة التنبيهات إذا كان ذلك ممكنًا
            if hasattr(self.reminders_widget, 'select_reminder'):
                self.reminders_widget.select_reminder(reminder_id)
            else:
                show_info_message("معلومات", f"تم الانتقال إلى قسم التنبيهات، ولكن لا يمكن تحديد التنبيه رقم {reminder_id}")
        else:
            show_info_message("معلومات", f"لا يمكن الانتقال إلى التنبيه رقم {reminder_id}")

    def mark_as_read(self):
        """تعليم الإشعار كمقروء"""
        selected_row = self.notifications_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار إشعار من القائمة")
            return

        notification_id = int(self.notifications_table.item(selected_row, 0).text())
        notification = self.session.query(Notification).get(notification_id)

        if not notification:
            show_error_message("خطأ", "لم يتم العثور على الإشعار")
            return

        if notification.is_read:
            show_info_message("معلومات", "الإشعار مقروء بالفعل")
            return

        notification.is_read = True
        self.session.commit()
        show_info_message("تم", "تم تعليم الإشعار كمقروء")
        self.refresh_data()

    def mark_as_unread(self):
        """تعليم الإشعار كغير مقروء"""
        selected_row = self.notifications_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار إشعار من القائمة")
            return

        notification_id = int(self.notifications_table.item(selected_row, 0).text())
        notification = self.session.query(Notification).get(notification_id)

        if not notification:
            show_error_message("خطأ", "لم يتم العثور على الإشعار")
            return

        if not notification.is_read:
            show_info_message("معلومات", "الإشعار غير مقروء بالفعل")
            return

        notification.is_read = False
        self.session.commit()
        show_info_message("تم", "تم تعليم الإشعار كغير مقروء")
        self.refresh_data()

    def delete_notification(self):
        """حذف إشعار"""
        selected_row = self.notifications_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار إشعار من القائمة")
            return

        notification_id = int(self.notifications_table.item(selected_row, 0).text())
        notification = self.session.query(Notification).get(notification_id)

        if not notification:
            show_error_message("خطأ", "لم يتم العثور على الإشعار")
            return

        # طلب تأكيد الحذف
        if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف الإشعار '{notification.title}'؟"):
            self.session.delete(notification)
            self.session.commit()
            show_info_message("تم", "تم حذف الإشعار بنجاح")
            self.refresh_data()

    def check_due_invoices(self):
        """التحقق من الفواتير المستحقة وإنشاء إشعارات لها"""
        from utils import check_due_invoices

        due_invoices = check_due_invoices(self.session)

        if due_invoices:
            show_info_message("تم", f"تم العثور على {len(due_invoices)} فاتورة مستحقة وإنشاء إشعارات لها")
        else:
            show_info_message("معلومات", "لا توجد فواتير مستحقة جديدة")

        self.refresh_data()

    def check_due_reminders(self):
        """التحقق من التنبيهات المستحقة وإنشاء إشعارات لها"""
        from utils import check_due_reminders

        due_reminders = check_due_reminders(self.session)

        if due_reminders:
            show_info_message("تم", f"تم العثور على {len(due_reminders)} تنبيه مستحق وإنشاء إشعارات لها")
        else:
            show_info_message("معلومات", "لا توجد تنبيهات مستحقة جديدة")

        self.refresh_data()



    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة مطابق للفواتير"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق للفواتير
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#047857', 'hover_mid': '#059669', 'hover_end': '#10b981', 'hover_bottom': '#34d399',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#052e16', 'pressed_border': '#064e3b',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.5)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#991b1b', 'hover_mid': '#dc2626', 'hover_end': '#ef4444', 'hover_bottom': '#f87171',
                    'hover_border': '#ef4444', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.5)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0284c7', 'bg_bottom': '#0ea5e9',
                    'hover_start': '#075985', 'hover_mid': '#0891b2', 'hover_end': '#0ea5e9', 'hover_bottom': '#38bdf8',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0284c7', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.5)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#14b8a6',
                    'hover_start': '#134e4a', 'hover_mid': '#0d9488', 'hover_end': '#14b8a6', 'hover_bottom': '#2dd4bf',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.5)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#06b6d4',
                    'hover_start': '#164e63', 'hover_mid': '#0891b2', 'hover_end': '#06b6d4', 'hover_bottom': '#22d3ee',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.5)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#ec4899',
                    'hover_start': '#831843', 'hover_mid': '#be185d', 'hover_end': '#ec4899', 'hover_bottom': '#f472b6',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.5)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#6366f1',
                    'hover_start': '#312e81', 'hover_mid': '#4f46e5', 'hover_end': '#6366f1', 'hover_bottom': '#818cf8',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4f46e5', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.5)'
                },
                'orange': {
                    'bg_start': '#431407', 'bg_mid': '#7c2d12', 'bg_end': '#9a3412', 'bg_bottom': '#f97316',
                    'hover_start': '#7c2d12', 'hover_mid': '#c2410c', 'hover_end': '#f97316', 'hover_bottom': '#fb923c',
                    'hover_border': '#f97316', 'pressed_start': '#431407', 'pressed_mid': '#7c2d12',
                    'pressed_end': '#9a3412', 'pressed_bottom': '#c2410c', 'pressed_border': '#9a3412',
                    'border': '#f97316', 'text': '#ffffff', 'shadow': 'rgba(249, 115, 22, 0.5)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات - مطابق للفواتير
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']},
                               0 0 30px rgba(255, 255, 255, 0.1);
                    letter-spacing: 0.3px;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 4px solid {color_scheme['hover_border']};
                    transform: translateY(-2px);
                    box-shadow: 0 8px 20px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 25px {color_scheme['shadow']},
                               0 0 40px rgba(255, 255, 255, 0.15);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px);
                    box-shadow: 0 3px 8px {color_scheme['shadow']},
                               inset 0 1px 0 rgba(255, 255, 255, 0.2),
                               inset 0 -1px 0 rgba(0, 0, 0, 0.5),
                               0 0 15px {color_scheme['shadow']};
                }}
                QPushButton::menu-indicator {{
                    {f"image: none; width: 0px;" if not has_menu else "width: 12px; height: 12px; margin-right: 4px;"}
                }}
            """

            button.setStyleSheet(style)

        except Exception as e:
            print(f"❌ خطأ في تطبيق تصميم الزر المتطور: {str(e)}")

    def style_compact_button(self, button, button_type):
        """تطبيق تصميم مدمج ومحسن على الأزرار"""
        try:
            # تحديد الألوان المحسنة للأزرار المدمجة
            colors = {
                'primary': {
                    'bg': '#3b82f6', 'hover': '#2563eb', 'pressed': '#1d4ed8',
                    'border': '#1d4ed8', 'text': '#ffffff'
                },
                'emerald': {
                    'bg': '#10b981', 'hover': '#059669', 'pressed': '#047857',
                    'border': '#059669', 'text': '#ffffff'
                },
                'danger': {
                    'bg': '#ef4444', 'hover': '#dc2626', 'pressed': '#b91c1c',
                    'border': '#dc2626', 'text': '#ffffff'
                },
                'info': {
                    'bg': '#0ea5e9', 'hover': '#0284c7', 'pressed': '#0369a1',
                    'border': '#0284c7', 'text': '#ffffff'
                },
                'modern_teal': {
                    'bg': '#14b8a6', 'hover': '#0d9488', 'pressed': '#0f766e',
                    'border': '#0d9488', 'text': '#ffffff'
                },
                'cyan': {
                    'bg': '#06b6d4', 'hover': '#0891b2', 'pressed': '#0e7490',
                    'border': '#0891b2', 'text': '#ffffff'
                },
                'rose': {
                    'bg': '#ec4899', 'hover': '#be185d', 'pressed': '#9d174d',
                    'border': '#be185d', 'text': '#ffffff'
                },
                'indigo': {
                    'bg': '#6366f1', 'hover': '#4f46e5', 'pressed': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff'
                },
                'orange': {
                    'bg': '#f97316', 'hover': '#ea580c', 'pressed': '#c2410c',
                    'border': '#ea580c', 'text': '#ffffff'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم المدمج
            style = f"""
                QPushButton {{
                    background-color: {color_scheme['bg']};
                    color: {color_scheme['text']};
                    border: 2px solid {color_scheme['border']};
                    border-radius: 6px;
                    padding: 6px 12px;
                    font-weight: bold;
                    font-size: 11px;
                    min-height: 24px;
                    max-height: 28px;
                    text-align: center;
                }}
                QPushButton:hover {{
                    background-color: {color_scheme['hover']};
                    border: 2px solid {color_scheme['hover']};
                }}
                QPushButton:pressed {{
                    background-color: {color_scheme['pressed']};
                    border: 2px solid {color_scheme['pressed']};
                }}
            """

            button.setStyleSheet(style)
            print(f"✅ تم تطبيق التصميم المدمج على الزر: {button.text()} - نوع: {button_type}")

        except Exception as e:
            print(f"❌ خطأ في تطبيق التصميم المدمج على الزر: {str(e)}")

