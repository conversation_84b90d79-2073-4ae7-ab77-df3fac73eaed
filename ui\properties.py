from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QFormLayout, QTextEdit, QHeaderView, QMessageBox,
                            QDialog, QComboBox, QGroupBox, QDateEdit, QDoubleSpinBox,
                            QTabWidget, QSplitter, QFrame, QFileDialog, QProgressBar,
                            QListWidget, QListWidgetItem, QCheckBox, QGridLayout, QSizePolicy,
                            QMenu, QAction, QTextBrowser)
from PyQt5.QtCore import Qt, QDate, QSize
from PyQt5.QtGui import QIcon, QFont, QColor, QPixmap

from database import (Project, Property, PropertyDocument, get_session)
from utils import (show_error_message, show_info_message, show_confirmation_message,
                    qdate_to_datetime, datetime_to_qdate, format_currency)
import datetime
import os
import shutil
from ui.unified_styles import UnifiedStyles, StyledButton, StyledTable, StyledLabel, StyledTabWidget

class PropertyDialog(QDialog):
    """نافذة حوار لإضافة أو تعديل عقار"""

    def __init__(self, parent=None, property_item=None, session=None):
        super().__init__(parent)
        self.property_item = property_item
        self.session = session
        self.init_ui()

    def init_ui(self):
        # إعداد نافذة الحوار
        if self.property_item:
            self.setWindowTitle("تعديل عقار")
        else:
            self.setWindowTitle("إضافة عقار جديد")

        self.setMinimumWidth(600)
        self.setMinimumHeight(500)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # إنشاء نموذج معلومات العقار
        form_group = QGroupBox("معلومات العقار")
        form_layout = QFormLayout()

        # حقل عنوان العقار
        self.title_edit = QLineEdit()
        if self.property_item:
            self.title_edit.setText(self.property_item.title)
        form_layout.addRow("عنوان العقار:", self.title_edit)

        # حقل نوع العقار
        self.type_combo = QComboBox()
        self.type_combo.addItems(["أرض", "شقة", "فيلا", "محل تجاري", "مكتب", "أخرى"])
        if self.property_item:
            index = self.type_combo.findText(self.property_item.type)
            if index >= 0:
                self.type_combo.setCurrentIndex(index)
        form_layout.addRow("نوع العقار:", self.type_combo)

        # حقل الموقع
        self.location_edit = QLineEdit()
        if self.property_item:
            self.location_edit.setText(self.property_item.location)
        form_layout.addRow("الموقع:", self.location_edit)

        # حقل المساحة
        self.area_edit = QDoubleSpinBox()
        self.area_edit.setRange(0, 100000)
        self.area_edit.setDecimals(0)  # إزالة الكسور
        self.area_edit.setSuffix(" م²")
        if self.property_item:
            self.area_edit.setValue(self.property_item.area)
        form_layout.addRow("المساحة:", self.area_edit)

        # حقل السعر
        self.price_edit = QDoubleSpinBox()
        self.price_edit.setRange(0, 1000000000)
        self.price_edit.setDecimals(0)  # إزالة الكسور
        self.price_edit.setSuffix(" جنيه")
        self.price_edit.setGroupSeparatorShown(True)
        if self.property_item:
            self.price_edit.setValue(self.property_item.price)
        form_layout.addRow("السعر:", self.price_edit)

        # حقل الحالة
        self.status_combo = QComboBox()
        self.status_combo.addItems(["متاح", "محجوز", "تم البيع"])
        if self.property_item:
            status_map = {"available": "متاح", "reserved": "محجوز", "sold": "تم البيع"}
            status = status_map.get(self.property_item.status, "متاح")
            index = self.status_combo.findText(status)
            if index >= 0:
                self.status_combo.setCurrentIndex(index)
        form_layout.addRow("الحالة:", self.status_combo)

        # حقل الوصف
        self.description_edit = QTextEdit()
        if self.property_item:
            self.description_edit.setText(self.property_item.description)
        form_layout.addRow("الوصف:", self.description_edit)

        # حقل المميزات
        self.features_edit = QTextEdit()
        if self.property_item:
            self.features_edit.setText(self.property_item.features)
        form_layout.addRow("المميزات:", self.features_edit)

        form_group.setLayout(form_layout)
        main_layout.addWidget(form_group)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()

        self.save_button = StyledButton("حفظ", "success", "normal")
        self.save_button.clicked.connect(self.accept)

        self.cancel_button = StyledButton("إلغاء", "secondary", "normal")
        self.cancel_button.clicked.connect(self.reject)

        buttons_layout.addWidget(self.save_button.button)
        buttons_layout.addWidget(self.cancel_button.button)

        main_layout.addLayout(buttons_layout)

        self.setLayout(main_layout)

    def get_data(self):
        """الحصول على بيانات العقار من النموذج"""
        title = self.title_edit.text().strip()
        property_type = self.type_combo.currentText()
        location = self.location_edit.text().strip()
        area = self.area_edit.value()
        price = self.price_edit.value()

        status_map = {"متاح": "available", "محجوز": "reserved", "تم البيع": "sold"}
        status = status_map.get(self.status_combo.currentText(), "available")

        description = self.description_edit.toPlainText().strip()
        features = self.features_edit.toPlainText().strip()

        # التحقق من صحة البيانات
        if not title:
            show_error_message("خطأ", "يجب إدخال عنوان العقار")
            return None

        return {
            'title': title,
            'type': property_type,
            'location': location,
            'area': area,
            'price': price,
            'status': status,
            'description': description,
            'features': features
        }

class PropertyDocumentDialog(QDialog):
    """نافذة حوار لإضافة أو تعديل وثيقة/صورة للعقار"""

    def __init__(self, parent=None, document=None, session=None, property_item=None):
        super().__init__(parent)
        self.document = document
        self.session = session
        self.property_item = property_item
        self.file_path = None
        self.init_ui()

    def init_ui(self):
        # إعداد نافذة الحوار
        if self.document:
            self.setWindowTitle("تعديل وثيقة/صورة")
        else:
            self.setWindowTitle("إضافة وثيقة/صورة جديدة")

        self.setMinimumWidth(500)
        self.setMinimumHeight(400)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # إنشاء نموذج معلومات الوثيقة
        form_layout = QFormLayout()

        # حقل العنوان
        self.title_edit = QLineEdit()
        if self.document:
            self.title_edit.setText(self.document.title)
        form_layout.addRow("العنوان:", self.title_edit)

        # حقل الملف
        file_layout = QHBoxLayout()
        self.file_path_label = QLineEdit()
        self.file_path_label.setReadOnly(True)
        if self.document:
            self.file_path = self.document.file_path
            self.file_path_label.setText(self.file_path)

        browse_button = StyledButton("استعراض...", "primary", "normal")
        browse_button.clicked.connect(self.browse_file)

        file_layout.addWidget(self.file_path_label)
        file_layout.addWidget(browse_button.button)
        form_layout.addRow("الملف:", file_layout)

        # خيار الصورة الرئيسية
        self.is_main_image = QCheckBox("صورة رئيسية للعقار")
        if self.document:
            self.is_main_image.setChecked(self.document.is_main_image)
        form_layout.addRow("", self.is_main_image)

        # حقل الوصف
        self.description_edit = QTextEdit()
        if self.document:
            self.description_edit.setText(self.document.description)
        form_layout.addRow("الوصف:", self.description_edit)

        main_layout.addLayout(form_layout)

        # معاينة الصورة
        self.preview_label = QLabel()
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setMinimumHeight(200)
        self.preview_label.setStyleSheet("border: 1px solid #ccc;")

        if self.document and self.document.file_path and os.path.exists(self.document.file_path):
            pixmap = QPixmap(self.document.file_path)
            if not pixmap.isNull():
                pixmap = pixmap.scaled(300, 200, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.preview_label.setPixmap(pixmap)

        main_layout.addWidget(QLabel("معاينة الصورة:"))
        main_layout.addWidget(self.preview_label)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()

        self.save_button = StyledButton("حفظ", "success", "normal")
        self.save_button.clicked.connect(self.accept)

        self.cancel_button = StyledButton("إلغاء", "secondary", "normal")
        self.cancel_button.clicked.connect(self.reject)

        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.cancel_button)

        main_layout.addLayout(buttons_layout)

        self.setLayout(main_layout)

    def browse_file(self):
        """استعراض واختيار ملف"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختر ملف", "", "جميع الملفات (*);;الصور (*.png *.jpg *.jpeg *.bmp);;المستندات (*.pdf *.doc *.docx)"
        )

        if file_path:
            self.file_path = file_path
            self.file_path_label.setText(file_path)

            # عرض معاينة للصورة إذا كانت صورة
            if file_path.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp')):
                pixmap = QPixmap(file_path)
                if not pixmap.isNull():
                    pixmap = pixmap.scaled(300, 200, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    self.preview_label.setPixmap(pixmap)
            else:
                self.preview_label.clear()
                self.preview_label.setText("لا يمكن عرض معاينة لهذا النوع من الملفات")

    def get_data(self):
        """الحصول على بيانات الوثيقة من النموذج"""
        title = self.title_edit.text().strip()
        description = self.description_edit.toPlainText().strip()
        is_main_image = self.is_main_image.isChecked()

        # التحقق من صحة البيانات
        if not title:
            show_error_message("خطأ", "يجب إدخال عنوان الوثيقة")
            return None

        if not self.file_path:
            show_error_message("خطأ", "يجب اختيار ملف")
            return None

        # تحديد نوع الملف
        file_extension = os.path.splitext(self.file_path)[1].lower()
        if file_extension in ['.jpg', '.jpeg', '.png', '.bmp', '.gif']:
            file_type = 'image'
        elif file_extension in ['.pdf']:
            file_type = 'pdf'
        elif file_extension in ['.doc', '.docx']:
            file_type = 'doc'
        else:
            file_type = 'other'

        # نسخ الملف إلى مجلد الوثائق
        documents_dir = os.path.join('documents', 'properties')
        os.makedirs(documents_dir, exist_ok=True)

        # إنشاء اسم فريد للملف
        file_name = f"{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}_{os.path.basename(self.file_path)}"
        new_file_path = os.path.join(documents_dir, file_name)

        # نسخ الملف
        try:
            shutil.copy2(self.file_path, new_file_path)
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء نسخ الملف: {str(e)}")
            return None

        return {
            'property_id': self.property_item.id,
            'title': title,
            'file_path': new_file_path,
            'file_type': file_type,
            'is_main_image': is_main_image,
            'upload_date': datetime.datetime.now(),
            'description': description
        }

class PropertyDocumentsDialog(QDialog):
    """نافذة حوار لإدارة وثائق وصور العقار"""

    def __init__(self, parent=None, property_item=None, session=None):
        super().__init__(parent)
        self.property_item = property_item
        self.session = session
        self.init_ui()
        self.refresh_data()

    def init_ui(self):
        # إعداد نافذة الحوار
        self.setWindowTitle(f"وثائق وصور العقار: {self.property_item.title}")
        self.setMinimumWidth(800)
        self.setMinimumHeight(600)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # إنشاء تخطيط مقسم
        splitter = QSplitter(Qt.Horizontal)

        # الجزء الأيمن: قائمة الوثائق
        documents_widget = QWidget()
        documents_layout = QVBoxLayout()

        documents_label = QLabel("الوثائق والصور:")
        documents_layout.addWidget(documents_label)

        self.documents_list = QListWidget()
        self.documents_list.setIconSize(QSize(64, 64))
        self.documents_list.itemSelectionChanged.connect(self.show_document_preview)
        documents_layout.addWidget(self.documents_list)

        # أزرار إدارة الوثائق
        documents_buttons_layout = QHBoxLayout()

        self.add_document_button = StyledButton("إضافة", "success", "normal")
        self.add_document_button.clicked.connect(self.add_document)

        self.edit_document_button = StyledButton("تعديل", "primary", "normal")
        self.edit_document_button.clicked.connect(self.edit_document)

        self.delete_document_button = StyledButton("حذف", "danger", "normal")
        self.delete_document_button.clicked.connect(self.delete_document)

        documents_buttons_layout.addWidget(self.add_document_button.button)
        documents_buttons_layout.addWidget(self.edit_document_button.button)
        documents_buttons_layout.addWidget(self.delete_document_button.button)

        documents_layout.addLayout(documents_buttons_layout)
        documents_widget.setLayout(documents_layout)

        # الجزء الأيسر: معاينة الوثيقة
        preview_widget = QWidget()
        preview_layout = QVBoxLayout()

        preview_label = QLabel("معاينة:")
        preview_layout.addWidget(preview_label)

        self.preview_image = QLabel()
        self.preview_image.setAlignment(Qt.AlignCenter)
        self.preview_image.setMinimumSize(400, 300)
        self.preview_image.setStyleSheet("border: 1px solid #ccc; background-color: #f5f5f5;")
        preview_layout.addWidget(self.preview_image)

        self.document_info = QTextEdit()
        self.document_info.setReadOnly(True)
        self.document_info.setMaximumHeight(100)
        preview_layout.addWidget(self.document_info)

        self.open_document_button = StyledButton("فتح الملف", "info", "normal")
        self.open_document_button.clicked.connect(self.open_document)
        preview_layout.addWidget(self.open_document_button.button)

        preview_widget.setLayout(preview_layout)

        # إضافة الأجزاء إلى المقسم
        splitter.addWidget(documents_widget)
        splitter.addWidget(preview_widget)

        # ضبط أحجام المقسم
        splitter.setSizes([300, 500])

        main_layout.addWidget(splitter)

        # زر الإغلاق
        close_button = StyledButton("إغلاق", "secondary", "normal")
        close_button.clicked.connect(self.accept)
        main_layout.addWidget(close_button.button)

        self.setLayout(main_layout)

    def refresh_data(self):
        """تحديث قائمة الوثائق"""
        self.documents_list.clear()

        documents = self.session.query(PropertyDocument).filter_by(property_id=self.property_item.id).all()

        for document in documents:
            item = QListWidgetItem(document.title)
            item.setData(Qt.UserRole, document.id)

            # تعيين أيقونة بناءً على نوع الملف
            if document.file_type == 'image':
                if os.path.exists(document.file_path):
                    pixmap = QPixmap(document.file_path)
                    if not pixmap.isNull():
                        pixmap = pixmap.scaled(64, 64, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                        item.setIcon(QIcon(pixmap))
                    else:
                        item.setIcon(QIcon("resources/icons/image.png"))
                else:
                    item.setIcon(QIcon("resources/icons/image.png"))
            elif document.file_type == 'pdf':
                item.setIcon(QIcon("resources/icons/pdf.png"))
            elif document.file_type == 'doc':
                item.setIcon(QIcon("resources/icons/doc.png"))
            else:
                item.setIcon(QIcon("resources/icons/file.png"))

            # تمييز الصورة الرئيسية
            if document.is_main_image:
                item.setText(f"{document.title} (الصورة الرئيسية)")
                font = item.font()
                font.setBold(True)
                item.setFont(font)

            self.documents_list.addItem(item)

    def show_document_preview(self):
        """عرض معاينة للوثيقة المحددة"""
        selected_items = self.documents_list.selectedItems()
        if not selected_items:
            self.preview_image.clear()
            self.document_info.clear()
            return

        document_id = selected_items[0].data(Qt.UserRole)
        document = self.session.query(PropertyDocument).get(document_id)

        if not document:
            return

        # عرض معلومات الوثيقة
        info_text = f"العنوان: {document.title}\n"
        info_text += f"النوع: {document.file_type}\n"
        if document.description:
            info_text += f"الوصف: {document.description}\n"
        info_text += f"تاريخ الرفع: {document.upload_date.strftime('%Y-%m-%d %H:%M')}"

        self.document_info.setText(info_text)

        # عرض معاينة للصورة إذا كانت صورة
        if document.file_type == 'image' and os.path.exists(document.file_path):
            pixmap = QPixmap(document.file_path)
            if not pixmap.isNull():
                pixmap = pixmap.scaled(400, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.preview_image.setPixmap(pixmap)
            else:
                self.preview_image.clear()
                self.preview_image.setText("لا يمكن عرض الصورة")
        else:
            self.preview_image.clear()
            if document.file_type == 'pdf':
                self.preview_image.setText("ملف PDF - اضغط على 'فتح الملف' للعرض")
            elif document.file_type == 'doc':
                self.preview_image.setText("ملف Word - اضغط على 'فتح الملف' للعرض")
            else:
                self.preview_image.setText("اضغط على 'فتح الملف' لفتح هذا الملف")

    def open_document(self):
        """فتح الوثيقة المحددة"""
        selected_items = self.documents_list.selectedItems()
        if not selected_items:
            return

        document_id = selected_items[0].data(Qt.UserRole)
        document = self.session.query(PropertyDocument).get(document_id)

        if not document or not os.path.exists(document.file_path):
            show_error_message("خطأ", "الملف غير موجود")
            return

        # فتح الملف باستخدام التطبيق الافتراضي
        import subprocess
        import platform

        try:
            if platform.system() == 'Windows':
                os.startfile(document.file_path)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.call(('open', document.file_path))
            else:  # Linux
                subprocess.call(('xdg-open', document.file_path))
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء فتح الملف: {str(e)}")

    def add_document(self):
        """إضافة وثيقة جديدة"""
        dialog = PropertyDocumentDialog(self, session=self.session, property_item=self.property_item)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # إنشاء وثيقة جديدة
                document = PropertyDocument(**data)

                # إذا كانت هذه الصورة الرئيسية، قم بإلغاء تعيين الصور الرئيسية الأخرى
                if document.is_main_image:
                    other_main_images = self.session.query(PropertyDocument).filter_by(
                        property_id=self.property_item.id, is_main_image=True
                    ).all()

                    for img in other_main_images:
                        img.is_main_image = False

                self.session.add(document)
                self.session.commit()
                show_info_message("تم", "تم إضافة الوثيقة بنجاح")
                self.refresh_data()

    def edit_document(self):
        """تعديل وثيقة"""
        selected_items = self.documents_list.selectedItems()
        if not selected_items:
            show_error_message("خطأ", "الرجاء اختيار وثيقة من القائمة")
            return

        document_id = selected_items[0].data(Qt.UserRole)
        document = self.session.query(PropertyDocument).get(document_id)

        if not document:
            show_error_message("خطأ", "لم يتم العثور على الوثيقة")
            return

        dialog = PropertyDocumentDialog(self, document, self.session, self.property_item)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # تحديث بيانات الوثيقة
                for key, value in data.items():
                    setattr(document, key, value)

                # إذا كانت هذه الصورة الرئيسية، قم بإلغاء تعيين الصور الرئيسية الأخرى
                if document.is_main_image:
                    other_main_images = self.session.query(PropertyDocument).filter(
                        PropertyDocument.property_id == self.property_item.id,
                        PropertyDocument.is_main_image == True,
                        PropertyDocument.id != document.id
                    ).all()

                    for img in other_main_images:
                        img.is_main_image = False

                self.session.commit()
                show_info_message("تم", "تم تحديث الوثيقة بنجاح")
                self.refresh_data()

    def delete_document(self):
        """حذف وثيقة"""
        selected_items = self.documents_list.selectedItems()
        if not selected_items:
            show_error_message("خطأ", "الرجاء اختيار وثيقة من القائمة")
            return

        document_id = selected_items[0].data(Qt.UserRole)
        document = self.session.query(PropertyDocument).get(document_id)

        if not document:
            show_error_message("خطأ", "لم يتم العثور على الوثيقة")
            return

        if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف الوثيقة '{document.title}'؟"):
            # حذف الملف من القرص
            if os.path.exists(document.file_path):
                try:
                    os.remove(document.file_path)
                except Exception as e:
                    print(f"خطأ في حذف الملف: {str(e)}")

            # حذف الوثيقة من قاعدة البيانات
            self.session.delete(document)
            self.session.commit()

            show_info_message("تم", "تم حذف الوثيقة بنجاح")
            self.refresh_data()

class PropertiesWidget(QWidget):
    """واجهة البيانات العامة للعقارات"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()
        self.refresh_data()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للموردين والعمال والمصروفات والإيرادات والمشاريع والفواتير والمخزون والمشتريات
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(8)

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للفواتير
        title_label = QLabel("🏠 البيانات العامة للعقارات - نظام شامل ومتقدم لإدارة العقارات مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للموردين
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث بالعنوان، الموقع أو الوصف...")
        self.search_edit.textChanged.connect(self.filter_properties)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 3px solid rgba(139, 92, 246, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(240, 249, 255, 0.9),
                    stop:1 rgba(224, 242, 254, 0.85));
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QLineEdit:hover {
                border: 3px solid rgba(124, 58, 237, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
            }
        """)

        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px;
                font-size: 20px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(91, 33, 182, 0.9),
                    stop:1 rgba(76, 29, 149, 0.8));
                border: 3px solid rgba(91, 33, 182, 0.9);
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
            }
        """)
        search_button.clicked.connect(self.filter_properties)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية النوع مطورة مطابقة للفواتير
        type_label = QLabel("🏢 نوع:")
        type_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        type_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إضافة حقل تصفية حسب النوع محسن
        self.type_filter = QComboBox()
        self.type_filter.addItem("جميع الأنواع", "")
        self.type_filter.addItem("أرض", "أرض")
        self.type_filter.addItem("شقة", "شقة")
        self.type_filter.addItem("فيلا", "فيلا")
        self.type_filter.addItem("محل تجاري", "محل تجاري")
        self.type_filter.addItem("مكتب", "مكتب")
        self.type_filter.currentIndexChanged.connect(self.filter_properties)
        self.type_filter.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                min-width: 120px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QComboBox:hover {
                border: 3px solid rgba(124, 58, 237, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
            }
            QComboBox:focus {
                border: 3px solid rgba(139, 92, 246, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(240, 249, 255, 0.9),
                    stop:1 rgba(224, 242, 254, 0.85));
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid rgba(96, 165, 250, 0.8);
                width: 8px;
                height: 8px;
                border-radius: 4px;
                background: rgba(96, 165, 250, 0.8);
            }
        """)

        # تسمية الحالة مطابقة للفواتير
        status_label = QLabel("🎯 حالة:")
        status_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        status_label.setAlignment(Qt.AlignCenter)

        # إضافة حقل تصفية حسب الحالة محسن
        self.status_filter = QComboBox()
        self.status_filter.addItem("جميع الحالات", "")
        self.status_filter.addItem("متاح", "available")
        self.status_filter.addItem("محجوز", "reserved")
        self.status_filter.addItem("تم البيع", "sold")
        self.status_filter.currentIndexChanged.connect(self.filter_properties)
        self.status_filter.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                min-width: 120px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QComboBox:hover {
                border: 3px solid rgba(124, 58, 237, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
            }
            QComboBox:focus {
                border: 3px solid rgba(139, 92, 246, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(240, 249, 255, 0.9),
                    stop:1 rgba(224, 242, 254, 0.85));
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid rgba(96, 165, 250, 0.8);
                width: 8px;
                height: 8px;
                border-radius: 4px;
                background: rgba(96, 165, 250, 0.8);
            }
        """)

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(type_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.type_filter, 1, Qt.AlignVCenter)
        search_layout.addWidget(status_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء جدول العقارات المتطور والمحسن
        self.create_advanced_properties_table()

        main_layout.addWidget(top_frame)
        main_layout.addWidget(self.properties_table, 1)  # إعطاء الجدول أولوية في التمدد

        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للموردين
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار بأحجام متناسقة حسب الترتيب الجديد مطابق للموردين

        # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة
        self.add_button = QPushButton("➕ إضافة عقار")
        self.style_advanced_button(self.add_button, 'emerald', has_menu=True)  # أخضر زمردي مميز مع قائمة
        self.add_button.clicked.connect(self.add_property)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')  # أزرق سماوي متطور مطابق للفواتير
        self.edit_button.clicked.connect(self.edit_property)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')  # أحمر تحذيري
        self.delete_button.clicked.connect(self.delete_property)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # تصميم حديث ومتطور
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الثانية - العمليات المتقدمة
        self.view_button = QPushButton("👁️ عرض التفاصيل ▼")
        self.style_advanced_button(self.view_button, 'indigo', has_menu=True)  # بنفسجي للتفاصيل
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة لعرض التفاصيل مطابقة للمشاريع
        from ui.unified_styles import UnifiedStyles
        view_menu = QMenu(self)
        view_menu.setStyleSheet(UnifiedStyles.get_menu_style('indigo', 'normal'))

        view_details_action = QAction("👁️ عرض التفاصيل", self)
        view_details_action.triggered.connect(self.view_property)
        view_menu.addAction(view_details_action)

        location_action = QAction("📍 عرض الموقع", self)
        location_action.triggered.connect(self.view_property_location)
        view_menu.addAction(location_action)

        images_action = QAction("🖼️ معرض الصور", self)
        images_action.triggered.connect(self.view_property_images)
        view_menu.addAction(images_action)

        valuation_action = QAction("💎 تقييم العقار", self)
        valuation_action.triggered.connect(self.view_property_valuation)
        view_menu.addAction(valuation_action)

        self.view_button.setMenu(view_menu)

        self.documents_button = QPushButton("📁 الوثائق والصور")
        self.style_advanced_button(self.documents_button, 'orange')  # برتقالي للوثائق
        self.documents_button.clicked.connect(self.manage_documents)
        self.documents_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'info', has_menu=True)  # لون متسق مع نظام الألوان
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة للتصدير مطابقة للأقسام الأخرى
        from ui.unified_styles import UnifiedStyles
        export_menu = QMenu(self)
        export_menu.setStyleSheet(UnifiedStyles.get_menu_style('info', 'normal'))

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(self.export_to_excel)
        export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(self.export_to_pdf)
        export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(self.export_to_csv)
        export_menu.addAction(csv_action)

        json_action = QAction("🔗 تصدير إلى JSON", self)
        json_action.triggered.connect(self.export_to_json)
        export_menu.addAction(json_action)

        self.export_button.setMenu(export_menu)

        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')  # وردي للإحصائيات
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إجمالي العقارات مطور ليتشابه مع الفواتير
        self.total_label = QLabel("إجمالي العقارات: 0")
        self.total_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                padding: 8px 16px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #064e3b,
                    stop:0.1 #047857,
                    stop:0.9 #065f46,
                    stop:1 #10b981);
                border: 5px solid #10b981;
                border-radius: 20px;
                min-height: 34px;
                max-height: 38px;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                           2px 2px 4px rgba(0, 0, 0, 0.7),
                           1px 1px 2px rgba(0, 0, 0, 0.5);
                box-shadow: 0 8px 20px rgba(16, 185, 129, 0.6),
                           inset 0 3px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(16, 185, 129, 0.6),
                           0 0 40px rgba(255, 255, 255, 0.1);
                letter-spacing: 0.5px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        """)
        self.total_label.setAlignment(Qt.AlignCenter)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.documents_button)
        actions_layout.addWidget(self.export_button)
        actions_layout.addWidget(self.statistics_button)
        actions_layout.addWidget(self.total_label)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        # تجميع التخطيط النهائي
        main_layout.addWidget(bottom_frame)

        self.setLayout(main_layout)

    def create_advanced_properties_table(self):
        """إنشاء جدول العقارات المتطور والمحسن مطابق للموردين"""
        styled_table = StyledTable()
        self.properties_table = styled_table.table
        self.properties_table.setColumnCount(7)
        self.properties_table.setHorizontalHeaderLabels(["الرقم", "العنوان", "النوع", "الموقع", "المساحة", "السعر", "الحالة"])

        # تحسين عرض الأعمدة
        header = self.properties_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # الرقم
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # العنوان
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # النوع
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # الموقع
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # المساحة
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # السعر
        header.setSectionResizeMode(6, QHeaderView.Fixed)  # الحالة

        # تحديد عرض الأعمدة الثابتة
        self.properties_table.setColumnWidth(0, 80)   # الرقم
        self.properties_table.setColumnWidth(2, 120)  # النوع
        self.properties_table.setColumnWidth(4, 100)  # المساحة
        self.properties_table.setColumnWidth(5, 150)  # السعر
        self.properties_table.setColumnWidth(6, 100)  # الحالة

        self.properties_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.properties_table.setSelectionMode(QTableWidget.SingleSelection)
        self.properties_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.properties_table.setAlternatingRowColors(True)

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للمصروفات والإيرادات والفواتير
        self.properties_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للمصروفات والإيرادات والفواتير
        header = self.properties_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                vertical-align: middle !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للمصروفات والإيرادات والفواتير
        self.properties_table.verticalHeader().setDefaultSectionSize(45)
        self.properties_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        # إضافة العلامة المائية للجدول مطابقة للفواتير
        self.add_watermark_to_properties_table()

    def add_watermark_to_properties_table(self):
        """إضافة علامة مائية لجدول العقارات مطابقة للفواتير"""
        try:
            # إنشاء العلامة المائية مثل الفواتير
            watermark = QLabel("Smart Finish", self.properties_table)
            watermark.setAlignment(Qt.AlignCenter)

            # تنسيق العلامة المائية مكبرة أكثر للتوحيد مع الباقي
            font = QFont("Arial", 150, QFont.Bold)
            watermark.setFont(font)

            # تطبيق تصميم محسن للعلامة المائية - أكثر وضوحاً لكن لا يؤثر على البيانات
            watermark.setStyleSheet("""
                QLabel {
                    color: rgba(71, 85, 105, 0.35);
                    background: transparent;
                    border: none;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
                    font-weight: bold;
                }
            """)

            # إصلاح مشكلة التفاعل - العلامة المائية لا تتداخل مع النقرات
            watermark.setAttribute(Qt.WA_TransparentForMouseEvents, True)
            watermark.setEnabled(False)  # تعطيل التفاعل مع العلامة المائية
            watermark.lower()  # وضع العلامة المائية في الخلف

            # تحسين إضافي للشفافية والوضوح
            watermark.setWindowOpacity(0.6)  # شفافية محسنة

            # تحديد موضع وحجم العلامة المائية مع تحسين الموضع
            def update_watermark_geometry():
                if self.properties_table.isVisible():
                    rect = self.properties_table.rect()
                    # تحسين الموضع ليكون في المنتصف مع مساحة أفضل
                    watermark.setGeometry(rect.x() + 50, rect.y() + 100,
                                        rect.width() - 100, rect.height() - 200)
                    watermark.lower()  # وضع العلامة المائية في الخلف دائماً
                    watermark.setAttribute(Qt.WA_TransparentForMouseEvents, True)

            # ربط تحديث الموضع بتغيير حجم الجدول
            original_resize_event = self.properties_table.resizeEvent
            def custom_resize_event(event):
                original_resize_event(event)
                update_watermark_geometry()
            self.properties_table.resizeEvent = custom_resize_event

            # تحديث أولي للموضع
            update_watermark_geometry()

        except Exception as e:
            print(f"خطأ في إضافة العلامة المائية للعقارات: {str(e)}")

    def refresh_data(self):
        """تحديث بيانات العقارات في الجدول"""
        # بناء الاستعلام
        query = self.session.query(Property)

        # تنفيذ الاستعلام
        properties = query.order_by(Property.created_at.desc()).all()

        # تحديث الجدول
        self.populate_table(properties)

    def populate_table(self, properties):
        """ملء جدول العقارات بالبيانات"""
        self.properties_table.setRowCount(0)

        for row, property_item in enumerate(properties):
            self.properties_table.insertRow(row)

            # إضافة بيانات العقار
            self.properties_table.setItem(row, 0, QTableWidgetItem(str(property_item.id)))
            self.properties_table.setItem(row, 1, QTableWidgetItem(property_item.title))
            self.properties_table.setItem(row, 2, QTableWidgetItem(property_item.type))
            self.properties_table.setItem(row, 3, QTableWidgetItem(property_item.location))
            self.properties_table.setItem(row, 4, QTableWidgetItem(f"{int(property_item.area)} م²"))
            self.properties_table.setItem(row, 5, QTableWidgetItem(f"{int(property_item.price):,} جنيه"))

            # حالة العقار
            status_map = {"available": "متاح", "reserved": "محجوز", "sold": "تم البيع"}
            status = status_map.get(property_item.status, "متاح")
            status_item = QTableWidgetItem(status)

            # تلوين حالة العقار
            if property_item.status == "available":
                status_item.setForeground(QColor("#27ae60"))  # أخضر
            elif property_item.status == "reserved":
                status_item.setForeground(QColor("#f39c12"))  # برتقالي
            elif property_item.status == "sold":
                status_item.setForeground(QColor("#e74c3c"))  # أحمر

            self.properties_table.setItem(row, 6, status_item)

            # تطبيق ألوان مختلفة على الصفوف
            for col in range(self.properties_table.columnCount()):
                item = self.properties_table.item(row, col)
                if item:
                    if row % 2 == 0:
                        item.setBackground(QColor("#f9f9f9"))
                    else:
                        item.setBackground(QColor("#ffffff"))

    def filter_properties(self):
        """تصفية العقارات بناءً على نص البحث والنوع والحالة"""
        search_text = self.search_edit.text().strip().lower()
        property_type = self.type_filter.currentData()
        status = self.status_filter.currentData()

        # بناء الاستعلام
        query = self.session.query(Property)

        # تطبيق تصفية النص
        if search_text:
            query = query.filter(
                Property.title.like(f"%{search_text}%") |
                Property.location.like(f"%{search_text}%") |
                Property.description.like(f"%{search_text}%")
            )

        # تطبيق تصفية النوع
        if property_type:
            query = query.filter(Property.type == property_type)

        # تطبيق تصفية الحالة
        if status:
            query = query.filter(Property.status == status)

        # تنفيذ الاستعلام
        properties = query.order_by(Property.created_at.desc()).all()

        # تحديث الجدول
        self.populate_table(properties)

    def add_property(self):
        """إضافة عقار جديد"""
        dialog = PropertyDialog(self, session=self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # إنشاء عقار جديد
                property_item = Property(**data)
                self.session.add(property_item)
                self.session.commit()

                show_info_message("تم", "تم إضافة العقار بنجاح")
                self.refresh_data()

    def edit_property(self):
        """تعديل عقار"""
        selected_row = self.properties_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عقار من القائمة")
            return

        property_id = int(self.properties_table.item(selected_row, 0).text())
        property_item = self.session.query(Property).get(property_id)

        if not property_item:
            show_error_message("خطأ", "لم يتم العثور على العقار")
            return

        dialog = PropertyDialog(self, property_item, self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # تحديث بيانات العقار
                for key, value in data.items():
                    setattr(property_item, key, value)

                # تحديث تاريخ التعديل
                property_item.updated_at = datetime.datetime.now()

                self.session.commit()
                show_info_message("تم", "تم تحديث العقار بنجاح")
                self.refresh_data()

    def delete_property(self):
        """حذف عقار"""
        selected_row = self.properties_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عقار من القائمة")
            return

        property_id = int(self.properties_table.item(selected_row, 0).text())
        property_item = self.session.query(Property).get(property_id)

        if not property_item:
            show_error_message("خطأ", "لم يتم العثور على العقار")
            return

        if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف العقار '{property_item.title}'؟"):
            # حذف الوثائق المرتبطة بالعقار
            documents = self.session.query(PropertyDocument).filter_by(property_id=property_id).all()
            for document in documents:
                # حذف الملف من القرص
                if os.path.exists(document.file_path):
                    try:
                        os.remove(document.file_path)
                    except Exception as e:
                        print(f"خطأ في حذف الملف: {str(e)}")

                # حذف الوثيقة من قاعدة البيانات
                self.session.delete(document)

            # حذف العقار
            self.session.delete(property_item)
            self.session.commit()

            show_info_message("تم", "تم حذف العقار بنجاح")
            self.refresh_data()

    def view_property(self):
        """عرض تفاصيل العقار"""
        selected_row = self.properties_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عقار من القائمة")
            return

        property_id = int(self.properties_table.item(selected_row, 0).text())
        property_item = self.session.query(Property).get(property_id)

        if not property_item:
            show_error_message("خطأ", "لم يتم العثور على العقار")
            return

        # إنشاء نافذة لعرض تفاصيل العقار
        dialog = QDialog(self)
        dialog.setWindowTitle(f"تفاصيل العقار: {property_item.title}")
        dialog.setMinimumWidth(800)
        dialog.setMinimumHeight(600)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # إنشاء تخطيط مقسم
        splitter = QSplitter(Qt.Horizontal)

        # الجزء الأيمن: معلومات العقار
        info_widget = QWidget()
        info_layout = QVBoxLayout()

        # عنوان العقار
        title_label = QLabel(property_item.title)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50;")
        info_layout.addWidget(title_label)

        # معلومات العقار
        info_grid = QGridLayout()

        # النوع
        info_grid.addWidget(QLabel("النوع:"), 0, 0)
        info_grid.addWidget(QLabel(property_item.type), 0, 1)

        # الموقع
        info_grid.addWidget(QLabel("الموقع:"), 1, 0)
        info_grid.addWidget(QLabel(property_item.location), 1, 1)

        # المساحة
        info_grid.addWidget(QLabel("المساحة:"), 2, 0)
        info_grid.addWidget(QLabel(f"{int(property_item.area)} م²"), 2, 1)

        # السعر
        info_grid.addWidget(QLabel("السعر:"), 3, 0)
        info_grid.addWidget(QLabel(f"{int(property_item.price):,} جنيه"), 3, 1)

        # الحالة
        info_grid.addWidget(QLabel("الحالة:"), 4, 0)
        status_map = {"available": "متاح", "reserved": "محجوز", "sold": "تم البيع"}
        status = status_map.get(property_item.status, "متاح")
        status_label = QLabel(status)

        # تلوين حالة العقار
        if property_item.status == "available":
            status_label.setStyleSheet("color: #27ae60;")  # أخضر
        elif property_item.status == "reserved":
            status_label.setStyleSheet("color: #f39c12;")  # برتقالي
        elif property_item.status == "sold":
            status_label.setStyleSheet("color: #e74c3c;")  # أحمر

        info_grid.addWidget(status_label, 5, 1)

        info_layout.addLayout(info_grid)

        # الوصف
        description_group = QGroupBox("الوصف")
        description_layout = QVBoxLayout()
        description_text = QTextEdit()
        description_text.setReadOnly(True)
        description_text.setText(property_item.description)
        description_layout.addWidget(description_text)
        description_group.setLayout(description_layout)
        info_layout.addWidget(description_group)

        # المميزات
        features_group = QGroupBox("المميزات")
        features_layout = QVBoxLayout()
        features_text = QTextEdit()
        features_text.setReadOnly(True)
        features_text.setText(property_item.features)
        features_layout.addWidget(features_text)
        features_group.setLayout(features_layout)
        info_layout.addWidget(features_group)

        info_widget.setLayout(info_layout)

        # الجزء الأيسر: صور العقار
        images_widget = QWidget()
        images_layout = QVBoxLayout()

        images_label = QLabel("صور العقار:")
        images_layout.addWidget(images_label)

        # البحث عن الصورة الرئيسية
        main_image = self.session.query(PropertyDocument).filter_by(
            property_id=property_item.id, is_main_image=True, file_type='image'
        ).first()

        # إذا لم تكن هناك صورة رئيسية، ابحث عن أي صورة
        if not main_image:
            main_image = self.session.query(PropertyDocument).filter_by(
                property_id=property_item.id, file_type='image'
            ).first()

        # عرض الصورة
        image_label = QLabel()
        image_label.setAlignment(Qt.AlignCenter)
        image_label.setMinimumSize(400, 300)
        image_label.setStyleSheet("border: 1px solid #ccc; background-color: #f5f5f5;")

        if main_image and os.path.exists(main_image.file_path):
            pixmap = QPixmap(main_image.file_path)
            if not pixmap.isNull():
                pixmap = pixmap.scaled(400, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                image_label.setPixmap(pixmap)
            else:
                image_label.setText("لا يمكن عرض الصورة")
        else:
            image_label.setText("لا توجد صور متاحة")

        images_layout.addWidget(image_label)

        # زر عرض جميع الصور والوثائق
        view_all_button = StyledButton("عرض جميع الصور والوثائق", "info", "normal")
        view_all_button.clicked.connect(lambda: self.manage_documents(property_item))
        images_layout.addWidget(view_all_button.button)

        images_widget.setLayout(images_layout)

        # إضافة الأجزاء إلى المقسم
        splitter.addWidget(info_widget)
        splitter.addWidget(images_widget)

        # ضبط أحجام المقسم
        splitter.setSizes([400, 400])

        main_layout.addWidget(splitter)

        # زر الإغلاق
        close_button = StyledButton("إغلاق", "secondary", "normal")
        close_button.clicked.connect(dialog.accept)
        main_layout.addWidget(close_button.button)

        dialog.setLayout(main_layout)
        dialog.exec_()

    def manage_documents(self, property_item=None):
        """إدارة وثائق وصور العقار"""
        if not property_item:
            selected_row = self.properties_table.currentRow()
            if selected_row < 0:
                show_error_message("خطأ", "الرجاء اختيار عقار من القائمة")
                return

            property_id = int(self.properties_table.item(selected_row, 0).text())
            property_item = self.session.query(Property).get(property_id)

            if not property_item:
                show_error_message("خطأ", "لم يتم العثور على العقار")
                return

        # إنشاء نافذة لإدارة الوثائق
        dialog = PropertyDocumentsDialog(self, property_item=property_item, session=self.session)
        dialog.exec_()

    def export_data(self):
        """تصدير بيانات العقارات"""
        show_info_message("قريباً", "ستتم إضافة هذه الميزة قريباً")

    def show_statistics(self):
        """عرض إحصائيات العقارات"""
        try:
            properties = self.session.query(Property).all()

            if not properties:
                show_info_message("إحصائيات العقارات", "لا توجد عقارات لعرض الإحصائيات")
                return

            # حساب الإحصائيات
            total_properties = len(properties)
            total_value = sum(property.price or 0 for property in properties)
            avg_value = total_value / total_properties if total_properties > 0 else 0
            total_area = sum(property.area or 0 for property in properties)
            avg_area = total_area / total_properties if total_properties > 0 else 0

            # إحصائيات حسب النوع
            type_stats = {}
            for property in properties:
                prop_type = property.type or 'غير محدد'
                if prop_type in type_stats:
                    type_stats[prop_type] += 1
                else:
                    type_stats[prop_type] = 1

            # إحصائيات حسب الحالة
            status_stats = {}
            for property in properties:
                status = property.status or 'غير محدد'
                if status in status_stats:
                    status_stats[status] += 1
                else:
                    status_stats[status] = 1

            # إحصائيات حسب الموقع
            location_stats = {}
            for property in properties:
                location = property.location or 'غير محدد'
                if location in location_stats:
                    location_stats[location] += 1
                else:
                    location_stats[location] = 1

            # إنشاء نافذة الإحصائيات
            dialog = QDialog(self)
            dialog.setWindowTitle("📊 إحصائيات العقارات")
            dialog.setModal(True)
            dialog.resize(700, 600)

            layout = QVBoxLayout()

            # الإحصائيات العامة
            general_stats = f"""
📊 الإحصائيات العامة:
─────────────────────────────────────────────────────────────────────────────
🏠 إجمالي العقارات: {total_properties}
💰 إجمالي القيمة: {int(total_value):,} جنيه
📊 متوسط القيمة: {int(avg_value):,} جنيه
📐 إجمالي المساحة: {int(total_area):,} م²
📏 متوسط المساحة: {int(avg_area):,} م²

🏘️ توزيع العقارات حسب النوع:
─────────────────────────────────────────────────────────────────────────────
"""

            for prop_type, count in type_stats.items():
                percentage = (count / total_properties) * 100
                general_stats += f"• {prop_type}: {count} عقار ({percentage:.1f}%)\n"

            general_stats += f"""
─────────────────────────────────────────────────────────────────────────────

📋 توزيع العقارات حسب الحالة:
─────────────────────────────────────────────────────────────────────────────
"""

            for status, count in status_stats.items():
                percentage = (count / total_properties) * 100
                general_stats += f"• {status}: {count} عقار ({percentage:.1f}%)\n"

            general_stats += f"""
─────────────────────────────────────────────────────────────────────────────

📍 توزيع العقارات حسب الموقع:
─────────────────────────────────────────────────────────────────────────────
"""

            # أفضل 5 مواقع
            sorted_locations = sorted(location_stats.items(), key=lambda x: x[1], reverse=True)[:5]
            for location, count in sorted_locations:
                percentage = (count / total_properties) * 100
                general_stats += f"• {location}: {count} عقار ({percentage:.1f}%)\n"

            # عرض الإحصائيات
            stats_text = QTextBrowser()
            stats_text.setPlainText(general_stats)
            stats_text.setStyleSheet("""
                QTextBrowser {
                    font-family: 'Segoe UI', Arial, sans-serif;
                    font-size: 11px;
                    line-height: 1.4;
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 15px;
                }
            """)
            layout.addWidget(stats_text)

            # أزرار الإجراءات
            buttons_layout = QHBoxLayout()

            export_stats_btn = QPushButton("📤 تصدير الإحصائيات")
            export_stats_btn.clicked.connect(lambda: self.export_statistics_report(general_stats))
            buttons_layout.addWidget(export_stats_btn)

            close_button = QPushButton("❌ إغلاق")
            close_button.clicked.connect(dialog.close)
            buttons_layout.addWidget(close_button)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في عرض الإحصائيات: {str(e)}")

    def export_statistics_report(self, stats_content):
        """تصدير تقرير الإحصائيات"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الإحصائيات", "إحصائيات_العقارات.txt", "Text Files (*.txt)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"""
═══════════════════════════════════════════════════════════════════════════════
                            📊 تقرير إحصائيات العقارات
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الإنشاء: {QDate.currentDate().toString('hh:mm:ss')}

{stats_content}

═══════════════════════════════════════════════════════════════════════════════
                                تم إنشاء التقرير بواسطة نظام إدارة العقارات
═══════════════════════════════════════════════════════════════════════════════
""")

                show_info_message("تم", f"تم تصدير تقرير الإحصائيات بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تصدير التقرير: {str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة مطابق للفواتير"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق للفواتير
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#047857', 'hover_mid': '#059669', 'hover_end': '#10b981', 'hover_bottom': '#34d399',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#052e16', 'pressed_border': '#064e3b',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.5)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#991b1b', 'hover_mid': '#dc2626', 'hover_end': '#ef4444', 'hover_bottom': '#f87171',
                    'hover_border': '#ef4444', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.5)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0284c7', 'bg_bottom': '#0ea5e9',
                    'hover_start': '#075985', 'hover_mid': '#0891b2', 'hover_end': '#0ea5e9', 'hover_bottom': '#38bdf8',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0284c7', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.5)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#14b8a6',
                    'hover_start': '#134e4a', 'hover_mid': '#0d9488', 'hover_end': '#14b8a6', 'hover_bottom': '#2dd4bf',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.5)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#06b6d4',
                    'hover_start': '#164e63', 'hover_mid': '#0891b2', 'hover_end': '#06b6d4', 'hover_bottom': '#22d3ee',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.5)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#ec4899',
                    'hover_start': '#831843', 'hover_mid': '#be185d', 'hover_end': '#ec4899', 'hover_bottom': '#f472b6',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.5)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#6366f1',
                    'hover_start': '#312e81', 'hover_mid': '#4f46e5', 'hover_end': '#6366f1', 'hover_bottom': '#818cf8',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4f46e5', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.5)'
                },
                'orange': {
                    'bg_start': '#431407', 'bg_mid': '#7c2d12', 'bg_end': '#9a3412', 'bg_bottom': '#f97316',
                    'hover_start': '#7c2d12', 'hover_mid': '#c2410c', 'hover_end': '#f97316', 'hover_bottom': '#fb923c',
                    'hover_border': '#f97316', 'pressed_start': '#431407', 'pressed_mid': '#7c2d12',
                    'pressed_end': '#9a3412', 'pressed_bottom': '#c2410c', 'pressed_border': '#9a3412',
                    'border': '#f97316', 'text': '#ffffff', 'shadow': 'rgba(249, 115, 22, 0.5)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات - مطابق للفواتير
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']},
                               0 0 30px rgba(255, 255, 255, 0.1);
                    letter-spacing: 0.3px;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 4px solid {color_scheme['hover_border']};
                    transform: translateY(-2px);
                    box-shadow: 0 8px 20px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 25px {color_scheme['shadow']},
                               0 0 40px rgba(255, 255, 255, 0.15);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px);
                    box-shadow: 0 3px 8px {color_scheme['shadow']},
                               inset 0 1px 0 rgba(255, 255, 255, 0.2),
                               inset 0 -1px 0 rgba(0, 0, 0, 0.5),
                               0 0 15px {color_scheme['shadow']};
                }}
                QPushButton::menu-indicator {{
                    {f"image: none; width: 0px;" if not has_menu else "width: 12px; height: 12px; margin-right: 4px;"}
                }}
            """

            button.setStyleSheet(style)

        except Exception as e:
            print(f"❌ خطأ في تطبيق تصميم الزر المتطور: {str(e)}")

    def export_to_excel(self):
        """تصدير العقارات إلى Excel"""
        self.export_to_csv()  # نفس الوظيفة

    def export_to_csv(self):
        """تصدير العقارات إلى CSV"""
        try:
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف CSV", "العقارات.csv", "CSV Files (*.csv)"
            )

            if file_path:
                properties = self.session.query(Property).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة رؤوس الأعمدة
                    writer.writerow(['الرقم', 'العنوان', 'النوع', 'المساحة', 'السعر', 'الحالة', 'الموقع'])

                    # كتابة البيانات
                    for property in properties:
                        writer.writerow([
                            property.id,
                            property.title,
                            property.type or "",
                            property.area or 0,
                            property.price or 0,
                            property.status or "",
                            property.location or ""
                        ])

                show_info_message("تم", f"تم تصدير العقارات بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير: {str(e)}")

    def export_to_pdf(self):
        """تصدير العقارات إلى PDF"""
        try:
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QTextDocument

            properties = self.session.query(Property).all()

            if not properties:
                show_info_message("تصدير PDF", "لا توجد عقارات للتصدير")
                return

            # حفظ ملف PDF مباشرة
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير العقارات", "تقرير_العقارات.pdf", "PDF Files (*.pdf)"
            )

            if file_path:
                # إنشاء محتوى HTML
                html_content = f"""
                <html dir="rtl">
                <head>
                    <meta charset="utf-8">
                    <title>تقرير العقارات</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 20px; }}
                        h1 {{ color: #16a085; text-align: center; }}
                        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                        th {{ background-color: #f2f2f2; }}
                    </style>
                </head>
                <body>
                    <h1>🏠 تقرير العقارات</h1>
                    <p><strong>تاريخ التقرير:</strong> {QDate.currentDate().toString('yyyy-MM-dd')}</p>

                    <table>
                        <tr>
                            <th>الرقم</th>
                            <th>العنوان</th>
                            <th>النوع</th>
                            <th>المساحة</th>
                            <th>السعر</th>
                            <th>الحالة</th>
                        </tr>
                """

                for property in properties:
                    area = int(property.area) if property.area else 0
                    price = int(property.price) if property.price else 0

                    html_content += f"""
                        <tr>
                            <td>{property.id}</td>
                            <td>{property.title}</td>
                            <td>{property.type or ""}</td>
                            <td>{area:,} م²</td>
                            <td>{price:,} جنيه</td>
                            <td>{property.status or ""}</td>
                        </tr>
                    """

                html_content += """
                    </table>
                </body>
                </html>
                """

                # إنشاء طابعة PDF
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)
                printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

                # إنشاء مستند وطباعته إلى PDF
                document = QTextDocument()
                document.setHtml(html_content)
                document.print_(printer)

                show_info_message("تم", f"تم تصدير العقارات إلى PDF بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تصدير PDF: {str(e)}")

    def export_to_json(self):
        """تصدير العقارات إلى JSON"""
        try:
            import json

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف JSON", "العقارات.json", "JSON Files (*.json)"
            )

            if file_path:
                properties = self.session.query(Property).all()

                properties_data = []
                for property in properties:
                    property_data = {
                        'id': property.id,
                        'title': property.title,
                        'type': property.type,
                        'area': float(property.area) if property.area else 0,
                        'price': float(property.price) if property.price else 0,
                        'status': property.status,
                        'location': property.location,
                        'description': property.description
                    }
                    properties_data.append(property_data)

                export_data = {
                    "export_info": {
                        "export_date": QDate.currentDate().toString('yyyy-MM-dd'),
                        "total_properties": len(properties_data),
                        "exported_by": "نظام إدارة العقارات"
                    },
                    "properties": properties_data
                }

                with open(file_path, 'w', encoding='utf-8') as jsonfile:
                    json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)

                show_info_message("تم", f"تم تصدير العقارات بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير: {str(e)}")

    def view_property_location(self):
        """عرض موقع العقار"""
        selected_row = self.properties_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عقار من القائمة")
            return

        property_id = int(self.properties_table.item(selected_row, 0).text())
        property = self.session.query(Property).get(property_id)

        if not property:
            show_error_message("خطأ", "لم يتم العثور على العقار")
            return

        # إنشاء نافذة عرض الموقع
        dialog = QDialog(self)
        dialog.setWindowTitle(f"📍 موقع العقار: {property.title}")
        dialog.setModal(True)
        dialog.resize(600, 500)

        layout = QVBoxLayout()

        # معلومات العقار
        info_text = f"""
📍 تفاصيل موقع العقار:
─────────────────────────────────────────────────────────────────────────────
🏠 العقار: {property.title}
📍 الموقع: {property.location or 'غير محدد'}
🏘️ النوع: {property.type or 'غير محدد'}
📐 المساحة: {int(property.area) if property.area else 0:,} م²
💰 السعر: {int(property.price) if property.price else 0:,} جنيه
📋 الحالة: {property.status or 'غير محدد'}
        """

        info_label = QLabel(info_text)
        info_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 11px;")
        layout.addWidget(info_label)

        # خريطة تجريبية
        map_frame = QFrame()
        map_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #ddd;
                border-radius: 10px;
                background-color: #e8f5e8;
                min-height: 200px;
            }
        """)

        map_layout = QVBoxLayout()

        map_icon = QLabel("🗺️")
        map_icon.setAlignment(Qt.AlignCenter)
        map_icon.setStyleSheet("font-size: 64px; color: #28a745;")
        map_layout.addWidget(map_icon)

        map_text = QLabel("خريطة تفاعلية للموقع")
        map_text.setAlignment(Qt.AlignCenter)
        map_text.setStyleSheet("font-size: 16px; font-weight: bold; color: #28a745;")
        map_layout.addWidget(map_text)

        map_desc = QLabel("في النسخة المكتملة ستعرض خريطة تفاعلية حقيقية للموقع")
        map_desc.setAlignment(Qt.AlignCenter)
        map_desc.setStyleSheet("font-size: 12px; color: #666;")
        map_desc.setWordWrap(True)
        map_layout.addWidget(map_desc)

        map_frame.setLayout(map_layout)
        layout.addWidget(map_frame)

        # معلومات إضافية عن الموقع
        location_details = f"""
🌍 تفاصيل الموقع:
─────────────────────────────────────────────────────────────────────────────
📍 العنوان الكامل: {property.location or 'غير محدد'}
🚗 المواصلات: متوفرة (تجريبي)
🏪 الخدمات القريبة: مدارس، مستشفيات، أسواق (تجريبي)
🏛️ المعالم القريبة: حدائق، مساجد، مراكز تجارية (تجريبي)
📊 تقييم الموقع: ممتاز (تجريبي)
        """

        details_label = QLabel(location_details)
        details_label.setStyleSheet("padding: 15px; background-color: #fff3cd; border-radius: 8px; font-size: 11px;")
        layout.addWidget(details_label)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        directions_btn = QPushButton("🧭 الاتجاهات")
        directions_btn.clicked.connect(lambda: show_info_message("قريباً", "ميزة الاتجاهات ستكون متاحة قريباً!"))
        buttons_layout.addWidget(directions_btn)

        nearby_btn = QPushButton("🏪 الخدمات القريبة")
        nearby_btn.clicked.connect(lambda: show_info_message("قريباً", "ميزة عرض الخدمات القريبة ستكون متاحة قريباً!"))
        buttons_layout.addWidget(nearby_btn)

        close_btn = QPushButton("❌ إغلاق")
        close_btn.clicked.connect(dialog.close)
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)
        dialog.setLayout(layout)
        dialog.exec_()

    def view_property_images(self):
        """عرض معرض صور العقار"""
        selected_row = self.properties_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عقار من القائمة")
            return

        property_id = int(self.properties_table.item(selected_row, 0).text())
        property = self.session.query(Property).get(property_id)

        if not property:
            show_error_message("خطأ", "لم يتم العثور على العقار")
            return

        # إنشاء نافذة معرض الصور
        dialog = QDialog(self)
        dialog.setWindowTitle(f"🖼️ معرض صور العقار: {property.title}")
        dialog.setModal(True)
        dialog.resize(900, 700)

        layout = QVBoxLayout()

        # معلومات العقار
        info_label = QLabel(f"🏠 العقار: {property.title} | 📍 الموقع: {property.location or 'غير محدد'}")
        info_label.setStyleSheet("font-weight: bold; font-size: 14px; padding: 10px; background-color: #f0f0f0; border-radius: 5px;")
        layout.addWidget(info_label)

        # منطقة التمرير للصور
        from PyQt5.QtWidgets import QScrollArea

        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        grid_layout = QGridLayout()

        # إضافة صور تجريبية للعقار
        property_images = [
            ("🏠 الواجهة الأمامية", "صورة الواجهة الرئيسية للعقار"),
            ("🚪 المدخل الرئيسي", "صورة المدخل والباب الأمامي"),
            ("🛋️ غرفة المعيشة", "صورة غرفة الجلوس الرئيسية"),
            ("🍽️ غرفة الطعام", "صورة غرفة الطعام والمطبخ"),
            ("🛏️ غرفة النوم الرئيسية", "صورة غرفة النوم الكبيرة"),
            ("🛁 الحمام", "صورة الحمام والمرافق الصحية"),
            ("🌳 الحديقة", "صورة الحديقة والمساحات الخضراء"),
            ("🚗 موقف السيارات", "صورة موقف السيارات والمرآب"),
            ("🏘️ المنطقة المحيطة", "صورة الحي والمنطقة المحيطة")
        ]

        row = 0
        col = 0
        for title, description in property_images:
            # إنشاء بطاقة للصورة
            image_card = QFrame()
            image_card.setStyleSheet("""
                QFrame {
                    border: 2px solid #ddd;
                    border-radius: 12px;
                    padding: 15px;
                    background-color: #f9f9f9;
                    margin: 5px;
                }
                QFrame:hover {
                    border-color: #16a085;
                    background-color: #e8f6f3;
                    transform: scale(1.02);
                }
            """)
            image_card.setFixedSize(250, 200)

            card_layout = QVBoxLayout()

            # أيقونة الصورة
            icon_label = QLabel("🖼️")
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setStyleSheet("font-size: 64px; color: #16a085;")
            card_layout.addWidget(icon_label)

            # عنوان الصورة
            title_label = QLabel(title)
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setStyleSheet("font-weight: bold; font-size: 13px; color: #2c3e50;")
            card_layout.addWidget(title_label)

            # وصف الصورة
            desc_label = QLabel(description)
            desc_label.setAlignment(Qt.AlignCenter)
            desc_label.setStyleSheet("font-size: 10px; color: #7f8c8d;")
            desc_label.setWordWrap(True)
            card_layout.addWidget(desc_label)

            image_card.setLayout(card_layout)
            grid_layout.addWidget(image_card, row, col)

            col += 1
            if col >= 3:  # 3 صور في كل صف
                col = 0
                row += 1

        scroll_widget.setLayout(grid_layout)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        add_image_btn = QPushButton("📷 إضافة صورة جديدة")
        add_image_btn.clicked.connect(lambda: show_info_message("قريباً", "ميزة إضافة الصور ستكون متاحة قريباً!"))
        buttons_layout.addWidget(add_image_btn)

        slideshow_btn = QPushButton("🎬 عرض شرائح")
        slideshow_btn.clicked.connect(lambda: show_info_message("قريباً", "ميزة عرض الشرائح ستكون متاحة قريباً!"))
        buttons_layout.addWidget(slideshow_btn)

        export_btn = QPushButton("📤 تصدير الصور")
        export_btn.clicked.connect(lambda: show_info_message("قريباً", "ميزة تصدير الصور ستكون متاحة قريباً!"))
        buttons_layout.addWidget(export_btn)

        close_btn = QPushButton("❌ إغلاق")
        close_btn.clicked.connect(dialog.close)
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)
        dialog.setLayout(layout)
        dialog.exec_()

    def view_property_valuation(self):
        """عرض تقييم العقار"""
        selected_row = self.properties_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عقار من القائمة")
            return

        property_id = int(self.properties_table.item(selected_row, 0).text())
        property = self.session.query(Property).get(property_id)

        if not property:
            show_error_message("خطأ", "لم يتم العثور على العقار")
            return

        # حساب التقييم التجريبي
        base_price = property.price or 0
        area = property.area or 0
        price_per_meter = base_price / area if area > 0 else 0

        # تقييمات تجريبية
        market_value = base_price * 1.1  # زيادة 10% عن السعر الحالي
        rental_value = base_price * 0.08 / 12  # 8% سنوياً مقسوم على 12 شهر
        investment_return = 8.5  # نسبة عائد استثماري تجريبية

        # إنشاء نافذة التقييم
        dialog = QDialog(self)
        dialog.setWindowTitle(f"💎 تقييم العقار: {property.title}")
        dialog.setModal(True)
        dialog.resize(700, 600)

        layout = QVBoxLayout()

        # معلومات العقار الأساسية
        basic_info = f"""
💎 تقييم العقار:
─────────────────────────────────────────────────────────────────────────────
🏠 العقار: {property.title}
📍 الموقع: {property.location or 'غير محدد'}
🏘️ النوع: {property.type or 'غير محدد'}
📐 المساحة: {int(area):,} م²
💰 السعر الحالي: {int(base_price):,} جنيه
📊 السعر لكل متر: {int(price_per_meter):,} جنيه/م²
        """

        basic_label = QLabel(basic_info)
        basic_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 11px;")
        layout.addWidget(basic_label)

        # التقييم المالي
        financial_evaluation = f"""
📊 التقييم المالي:
─────────────────────────────────────────────────────────────────────────────
💹 القيمة السوقية المقدرة: {int(market_value):,} جنيه
🏠 قيمة الإيجار الشهري المقدرة: {int(rental_value):,} جنيه
📈 العائد الاستثماري المتوقع: {investment_return}% سنوياً
💰 الربح المتوقع من البيع: {int(market_value - base_price):,} جنيه
📊 نسبة الزيادة: {((market_value - base_price) / base_price * 100):.1f}%
        """

        financial_label = QLabel(financial_evaluation)
        financial_label.setStyleSheet("padding: 15px; background-color: #d4edda; border-radius: 8px; font-size: 11px;")
        layout.addWidget(financial_label)

        # تقييم الموقع والمميزات
        location_evaluation = f"""
🌟 تقييم الموقع والمميزات:
─────────────────────────────────────────────────────────────────────────────
📍 تقييم الموقع: ممتاز (9/10)
🚗 سهولة المواصلات: جيد جداً (8/10)
🏪 قرب الخدمات: ممتاز (9/10)
🏫 قرب المدارس: جيد (7/10)
🏥 قرب المستشفيات: جيد جداً (8/10)
🛒 قرب الأسواق: ممتاز (9/10)
🌳 البيئة المحيطة: جيد جداً (8/10)
        """

        location_label = QLabel(location_evaluation)
        location_label.setStyleSheet("padding: 15px; background-color: #fff3cd; border-radius: 8px; font-size: 11px;")
        layout.addWidget(location_label)

        # التوصيات
        recommendations = f"""
💡 التوصيات:
─────────────────────────────────────────────────────────────────────────────
✅ العقار مناسب للاستثمار بعائد جيد
✅ الموقع ممتاز ومطلوب في السوق
✅ إمكانية زيادة القيمة مع الوقت
⚠️ يُنصح بمراجعة حالة العقار قبل الشراء
💼 مناسب للإيجار أو البيع
📈 توقعات نمو إيجابية للمنطقة
        """

        recommendations_label = QLabel(recommendations)
        recommendations_label.setStyleSheet("padding: 15px; background-color: #cce5ff; border-radius: 8px; font-size: 11px;")
        layout.addWidget(recommendations_label)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        detailed_report_btn = QPushButton("📋 تقرير مفصل")
        detailed_report_btn.clicked.connect(lambda: self.generate_valuation_report(property, market_value, rental_value, investment_return))
        buttons_layout.addWidget(detailed_report_btn)

        compare_btn = QPushButton("⚖️ مقارنة مع عقارات مشابهة")
        compare_btn.clicked.connect(lambda: show_info_message("قريباً", "ميزة المقارنة ستكون متاحة قريباً!"))
        buttons_layout.addWidget(compare_btn)

        close_btn = QPushButton("❌ إغلاق")
        close_btn.clicked.connect(dialog.close)
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)
        dialog.setLayout(layout)
        dialog.exec_()

    def generate_valuation_report(self, property, market_value, rental_value, investment_return):
        """إنشاء تقرير تقييم مفصل"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير التقييم", f"تقييم_{property.title}.txt", "Text Files (*.txt)"
            )

            if file_path:
                report_content = f"""
═══════════════════════════════════════════════════════════════════════════════
                                💎 تقرير تقييم العقار
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ التقييم: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الإنشاء: {QDate.currentDate().toString('hh:mm:ss')}

🏠 معلومات العقار:
─────────────────────────────────────────────────────────────────────────────
العنوان: {property.title}
الموقع: {property.location or 'غير محدد'}
النوع: {property.type or 'غير محدد'}
المساحة: {int(property.area) if property.area else 0:,} م²
السعر الحالي: {int(property.price) if property.price else 0:,} جنيه

💹 التقييم المالي:
─────────────────────────────────────────────────────────────────────────────
القيمة السوقية المقدرة: {int(market_value):,} جنيه
قيمة الإيجار الشهري: {int(rental_value):,} جنيه
العائد الاستثماري: {investment_return}% سنوياً
الربح المتوقع: {int(market_value - (property.price or 0)):,} جنيه

🌟 التقييم العام:
─────────────────────────────────────────────────────────────────────────────
تقييم الموقع: ممتاز
إمكانية الاستثمار: عالية
التوصية: مناسب للشراء/الاستثمار

ملاحظة: هذا تقييم تجريبي. يُنصح بالحصول على تقييم مهني معتمد.

═══════════════════════════════════════════════════════════════════════════════
                        تم إنشاء التقرير بواسطة نظام إدارة العقارات
═══════════════════════════════════════════════════════════════════════════════
"""

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(report_content)

                show_info_message("تم", f"تم حفظ تقرير التقييم بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في حفظ التقرير: {str(e)}")
