from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QFormLayout, QTextEdit, QHeaderView, QMessageBox,
                            QDialog, QComboBox, QGroupBox, QDateEdit, QDoubleSpinBox,
                            QTabWidget, QSplitter, QFrame, QFileDialog, QProgressBar,
                            QMenu, QAction, QSizePolicy, QListWidget, QListWidgetItem,
                            QTextBrowser, QScrollArea, QGridLayout)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QIcon, QFont, QColor

from database import (Project, Client, Document, Property, PropertyDocument, get_session)
from utils import (show_error_message, show_info_message, show_confirmation_message,
                    qdate_to_datetime, datetime_to_qdate, format_currency)
from ui.properties import PropertiesWidget
from ui.unified_styles import UnifiedStyles, StyledButton, StyledTable, StyledTabWidget, StyledGroupBox
import datetime

class ProjectDialog(QDialog):
    """نافذة حوار لإضافة أو تعديل مشروع"""

    def __init__(self, parent=None, project=None, session=None):
        super().__init__(parent)
        self.project = project
        self.session = session
        self.init_ui()

    def init_ui(self):
        # إعداد نافذة الحوار
        if self.project:
            self.setWindowTitle("تعديل مشروع")
        else:
            self.setWindowTitle("إضافة مشروع جديد")

        self.setMinimumWidth(600)
        self.setMinimumHeight(500)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # إنشاء نموذج معلومات المشروع
        form_group = QGroupBox("معلومات المشروع")
        form_layout = QFormLayout()

        # حقل اسم المشروع
        self.name_edit = QLineEdit()
        self.name_edit.setStyleSheet("QLineEdit { padding: 8px; border: 2px solid #E2E8F0; border-radius: 6px; }")
        if self.project:
            self.name_edit.setText(self.project.name)
        form_layout.addRow("اسم المشروع:", self.name_edit)

        # حقل العميل
        self.client_combo = QComboBox()
        self.client_combo.addItem("-- اختر عميل --", None)

        # إضافة العملاء من قاعدة البيانات
        if self.session:
            clients = self.session.query(Client).all()
            for client in clients:
                self.client_combo.addItem(client.name, client.id)

        # تحديد العميل الحالي إذا كان موجودًا
        if self.project and self.project.client_id:
            index = self.client_combo.findData(self.project.client_id)
            if index >= 0:
                self.client_combo.setCurrentIndex(index)

        form_layout.addRow("العميل:", self.client_combo)

        # حقل الموقع
        self.location_edit = QLineEdit()
        if self.project:
            self.location_edit.setText(self.project.location)
        form_layout.addRow("الموقع:", self.location_edit)

        # حقل المساحة
        self.area_edit = QDoubleSpinBox()
        self.area_edit.setRange(0, 10000)
        self.area_edit.setDecimals(0)  # بدون كسور عشرية
        self.area_edit.setSuffix(" م²")
        if self.project:
            self.area_edit.setValue(self.project.area or 0)
        form_layout.addRow("المساحة:", self.area_edit)

        # حقل تاريخ البدء
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDate.currentDate())
        if self.project and self.project.start_date:
            self.start_date_edit.setDate(datetime_to_qdate(self.project.start_date))
        form_layout.addRow("تاريخ البدء:", self.start_date_edit)

        # حقل تاريخ الانتهاء المتوقع
        self.expected_end_date_edit = QDateEdit()
        self.expected_end_date_edit.setCalendarPopup(True)
        self.expected_end_date_edit.setDate(QDate.currentDate().addMonths(1))
        if self.project and self.project.expected_end_date:
            self.expected_end_date_edit.setDate(datetime_to_qdate(self.project.expected_end_date))
        form_layout.addRow("تاريخ الانتهاء المتوقع:", self.expected_end_date_edit)

        # حقل تاريخ الانتهاء الفعلي
        self.actual_end_date_edit = QDateEdit()
        self.actual_end_date_edit.setCalendarPopup(True)
        self.actual_end_date_edit.setDate(QDate.currentDate())
        if self.project and self.project.actual_end_date:
            self.actual_end_date_edit.setDate(datetime_to_qdate(self.project.actual_end_date))
        form_layout.addRow("تاريخ الانتهاء الفعلي:", self.actual_end_date_edit)

        # حقل الحالة
        self.status_combo = QComboBox()
        statuses = ["planning", "in_progress", "completed", "cancelled"]
        status_labels = ["قيد التخطيط", "قيد التنفيذ", "مكتمل", "ملغى"]

        for i, status in enumerate(statuses):
            self.status_combo.addItem(status_labels[i], status)

        if self.project and self.project.status:
            index = self.status_combo.findData(self.project.status)
            if index >= 0:
                self.status_combo.setCurrentIndex(index)

        form_layout.addRow("الحالة:", self.status_combo)

        # حقل الميزانية
        self.budget_edit = QDoubleSpinBox()
        self.budget_edit.setRange(0, 10000000)
        self.budget_edit.setDecimals(0)  # بدون كسور عشرية
        self.budget_edit.setSingleStep(1000)
        if self.project:
            self.budget_edit.setValue(self.project.budget or 0)
        form_layout.addRow("الميزانية:", self.budget_edit)

        form_group.setLayout(form_layout)

        # حقل الوصف
        description_group = QGroupBox("وصف المشروع")
        description_layout = QVBoxLayout()

        self.description_edit = QTextEdit()
        if self.project and self.project.description:
            self.description_edit.setText(self.project.description)

        description_layout.addWidget(self.description_edit)
        description_group.setLayout(description_layout)

        # حقل الملاحظات
        notes_group = QGroupBox("ملاحظات")
        notes_layout = QVBoxLayout()

        self.notes_edit = QTextEdit()
        if self.project and self.project.notes:
            self.notes_edit.setText(self.project.notes)

        notes_layout.addWidget(self.notes_edit)
        notes_group.setLayout(notes_layout)

        # أزرار الحفظ والإلغاء
        button_layout = QHBoxLayout()

        self.save_button = QPushButton("💾 حفظ")
        self.save_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #064e3b, stop:0.15 #047857, stop:0.85 #065f46, stop:1 #10b981);
                color: #ffffff;
                border: 4px solid #10b981;
                border-radius: 16px;
                padding: 8px 16px;
                font-weight: 900;
                font-size: 13px;
                min-height: 38px;
                max-height: 38px;
                min-width: 100px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                box-shadow: 0 6px 15px rgba(16, 185, 129, 0.5);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #047857, stop:0.15 #059669, stop:0.85 #10b981, stop:1 #34d399);
                border: 4px solid #10b981;
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(16, 185, 129, 0.5);
            }
        """)
        self.save_button.clicked.connect(self.accept)

        self.cancel_button = QPushButton("❌ إلغاء")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #7f1d1d, stop:0.15 #991b1b, stop:0.85 #b91c1c, stop:1 #dc2626);
                color: #ffffff;
                border: 4px solid #dc2626;
                border-radius: 16px;
                padding: 8px 16px;
                font-weight: 900;
                font-size: 13px;
                min-height: 38px;
                max-height: 38px;
                min-width: 100px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                box-shadow: 0 6px 15px rgba(220, 38, 38, 0.5);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #991b1b, stop:0.15 #dc2626, stop:0.85 #ef4444, stop:1 #f87171);
                border: 4px solid #ef4444;
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(220, 38, 38, 0.5);
            }
        """)
        self.cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)

        # تجميع التخطيط النهائي
        main_layout.addWidget(form_group)
        main_layout.addWidget(description_group)
        main_layout.addWidget(notes_group)
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

    def get_data(self):
        """الحصول على بيانات المشروع من النموذج"""
        name = self.name_edit.text().strip()
        client_id = self.client_combo.currentData()
        location = self.location_edit.text().strip()
        area = self.area_edit.value()
        start_date = qdate_to_datetime(self.start_date_edit.date())
        expected_end_date = qdate_to_datetime(self.expected_end_date_edit.date())
        # تعيين تاريخ الانتهاء الفعلي بغض النظر عن حالة المشروع
        actual_end_date = qdate_to_datetime(self.actual_end_date_edit.date())
        status = self.status_combo.currentData()
        budget = self.budget_edit.value()
        description = self.description_edit.toPlainText().strip()
        notes = self.notes_edit.toPlainText().strip()

        # التحقق من صحة البيانات
        if not name:
            show_error_message("خطأ", "يجب إدخال اسم المشروع")
            return None

        if not client_id:
            show_error_message("خطأ", "يجب اختيار عميل")
            return None

        if expected_end_date <= start_date:
            show_error_message("خطأ", "يجب أن يكون تاريخ الانتهاء المتوقع بعد تاريخ البدء")
            return None

        return {
            'name': name,
            'client_id': client_id,
            'location': location,
            'area': area,
            'start_date': start_date,
            'expected_end_date': expected_end_date,
            'actual_end_date': actual_end_date,
            'status': status,
            'budget': budget,
            'description': description,
            'notes': notes
        }

class ProjectsWidget(QWidget):
    """واجهة إدارة المشاريع"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()
        self.refresh_data()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للعمال
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(8)

        # إنشاء تبويبات للإنشاء والإستثمار والبيانات العامة للعقارات مع تنسيق مطابق للعمال
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 3px solid #000000;
                border-radius: 8px;
                background: #ffffff;
                margin-top: -1px;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e2e8f0, stop:0.5 #cbd5e1, stop:1 #94a3b8);
                color: #1e293b;
                border: 3px solid #000000;
                border-bottom: none;
                border-radius: 8px 8px 0 0;
                padding: 8px 32px;
                margin: 2px;
                font-size: 16px;
                font-weight: bold;
                min-width: 400px;
                max-width: 600px;
                min-height: 35px;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4285f4, stop:0.5 #1a73e8, stop:1 #1557b0);
                color: white;
                border: 3px solid #000000;
                border-bottom: none;
                margin-top: -1px;
                padding: 9px 32px;
                font-size: 17px;
                min-height: 35px;
                max-height: 40px;
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5a67d8, stop:0.5 #667eea, stop:1 #e0c3fc);
                border: 2px solid #000000;
            }
        """)
        # تبويب الإنشاء والإستثمار
        projects_tab = QWidget()
        projects_layout = QVBoxLayout()
        projects_layout.setContentsMargins(5, 5, 5, 5)
        projects_layout.setSpacing(8)

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للفواتير
        title_label = QLabel("🏗️ إدارة المشاريع المتطورة - نظام شامل ومتقدم لإدارة المشاريع مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        projects_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للموردين
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث بالاسم، الموقع، العميل أو الوصف...")
        self.search_edit.textChanged.connect(self.filter_projects)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 3px solid rgba(139, 92, 246, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(240, 249, 255, 0.9),
                    stop:1 rgba(224, 242, 254, 0.85));
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QLineEdit:hover {
                border: 3px solid rgba(124, 58, 237, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
            }
        """)

        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px;
                font-size: 20px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(91, 33, 182, 0.9),
                    stop:1 rgba(76, 29, 149, 0.8));
                border: 3px solid rgba(91, 33, 182, 0.9);
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
            }
        """)
        search_button.clicked.connect(self.filter_projects)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية مطورة مطابقة للفواتير
        filter_label = QLabel("🎯 حالة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إضافة حقل تصفية حسب الحالة محسن
        self.status_filter = QComboBox()
        self.status_filter.addItem("جميع الحالات", None)
        self.status_filter.addItem("قيد التخطيط", "planning")
        self.status_filter.addItem("قيد التنفيذ", "in_progress")
        self.status_filter.addItem("مكتملة", "completed")
        self.status_filter.addItem("ملغاة", "cancelled")
        self.status_filter.currentIndexChanged.connect(self.filter_projects)
        self.status_filter.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                min-width: 120px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QComboBox:hover {
                border: 3px solid rgba(124, 58, 237, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
            }
            QComboBox:focus {
                border: 3px solid rgba(139, 92, 246, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(240, 249, 255, 0.9),
                    stop:1 rgba(224, 242, 254, 0.85));
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid rgba(96, 165, 250, 0.8);
                width: 8px;
                height: 8px;
                border-radius: 4px;
                background: rgba(96, 165, 250, 0.8);
            }
        """)

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء جدول المشاريع المتطور والمحسن
        self.create_advanced_projects_table()

        projects_layout.addWidget(top_frame)
        projects_layout.addWidget(self.projects_table, 1)  # إعطاء الجدول أولوية في التمدد

        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للموردين
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار بأحجام متناسقة حسب الترتيب الجديد مطابق للموردين

        # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة
        self.add_button = QPushButton("➕ إضافة مشروع")
        self.style_advanced_button(self.add_button, 'emerald', has_menu=True)  # أخضر زمردي مميز مع قائمة
        self.add_button.clicked.connect(self.add_project)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')  # أزرق سماوي متطور مطابق للفواتير
        self.edit_button.clicked.connect(self.edit_project)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')  # أحمر تحذيري
        self.delete_button.clicked.connect(self.delete_project)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # تصميم حديث ومتطور
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الثانية - العمليات المتقدمة
        self.view_button = QPushButton("👁️ عرض التفاصيل ▼")
        self.style_advanced_button(self.view_button, 'indigo', has_menu=True)  # بنفسجي للتفاصيل
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة لعرض التفاصيل مطابقة للأقسام الأخرى
        from ui.unified_styles import UnifiedStyles
        view_menu = QMenu(self)
        view_menu.setStyleSheet(UnifiedStyles.get_menu_style('indigo', 'normal'))

        view_details_action = QAction("👁️ عرض التفاصيل", self)
        view_details_action.triggered.connect(self.view_project)
        view_menu.addAction(view_details_action)

        timeline_action = QAction("📅 الجدول الزمني", self)
        timeline_action.triggered.connect(self.view_project_timeline)
        view_menu.addAction(timeline_action)

        budget_action = QAction("💰 تفاصيل الميزانية", self)
        budget_action.triggered.connect(self.view_project_budget)
        view_menu.addAction(budget_action)

        progress_action = QAction("📊 تقرير التقدم", self)
        progress_action.triggered.connect(self.view_project_progress)
        view_menu.addAction(progress_action)

        self.view_button.setMenu(view_menu)

        self.documents_button = QPushButton("📁 الوثائق ▼")
        self.style_advanced_button(self.documents_button, 'orange', has_menu=True)  # برتقالي للوثائق
        self.documents_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة للوثائق مطابقة للأقسام الأخرى
        from ui.unified_styles import UnifiedStyles
        docs_menu = QMenu(self)
        docs_menu.setStyleSheet(UnifiedStyles.get_menu_style('orange', 'normal'))

        manage_docs_action = QAction("📁 إدارة الوثائق", self)
        manage_docs_action.triggered.connect(self.manage_documents)
        docs_menu.addAction(manage_docs_action)

        add_doc_action = QAction("📄 إضافة وثيقة", self)
        add_doc_action.triggered.connect(self.add_document)
        docs_menu.addAction(add_doc_action)

        add_image_action = QAction("🖼️ إضافة صورة", self)
        add_image_action.triggered.connect(self.add_image)
        docs_menu.addAction(add_image_action)

        view_gallery_action = QAction("🖼️ معرض الصور", self)
        view_gallery_action.triggered.connect(self.view_image_gallery)
        docs_menu.addAction(view_gallery_action)

        export_docs_action = QAction("📤 تصدير الوثائق", self)
        export_docs_action.triggered.connect(self.export_documents)
        docs_menu.addAction(export_docs_action)

        self.documents_button.setMenu(docs_menu)

        self.report_button = QPushButton("📋 التقارير")
        self.style_advanced_button(self.report_button, 'cyan')  # سيان مميز للتقارير
        self.report_button.clicked.connect(self.generate_projects_report)
        self.report_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'info', has_menu=True)  # لون متسق مع نظام الألوان
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة للتصدير مطابقة للأقسام الأخرى
        from ui.unified_styles import UnifiedStyles
        export_menu = QMenu(self)
        export_menu.setStyleSheet(UnifiedStyles.get_menu_style('info', 'normal'))

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(self.export_to_excel)
        export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(self.export_to_pdf)
        export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(self.export_to_csv)
        export_menu.addAction(csv_action)

        json_action = QAction("🔗 تصدير إلى JSON", self)
        json_action.triggered.connect(self.export_to_json)
        export_menu.addAction(json_action)

        self.export_button.setMenu(export_menu)

        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')  # وردي للإحصائيات
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إجمالي المشاريع مطور ليتشابه مع الفواتير
        self.total_label = QLabel("إجمالي المشاريع: 0")
        self.total_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                padding: 8px 16px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #064e3b,
                    stop:0.1 #047857,
                    stop:0.9 #065f46,
                    stop:1 #10b981);
                border: 5px solid #10b981;
                border-radius: 20px;
                min-height: 34px;
                max-height: 38px;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                           2px 2px 4px rgba(0, 0, 0, 0.7),
                           1px 1px 2px rgba(0, 0, 0, 0.5);
                box-shadow: 0 8px 20px rgba(16, 185, 129, 0.6),
                           inset 0 3px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(16, 185, 129, 0.6),
                           0 0 40px rgba(255, 255, 255, 0.1);
                letter-spacing: 0.5px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        """)
        self.total_label.setAlignment(Qt.AlignCenter)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.documents_button)
        actions_layout.addWidget(self.report_button)
        actions_layout.addWidget(self.export_button)
        actions_layout.addWidget(self.statistics_button)
        actions_layout.addWidget(self.total_label)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        # تجميع تخطيط تبويب المشاريع
        projects_layout.addWidget(bottom_frame)
        projects_tab.setLayout(projects_layout)

        # تبويب البيانات العامة للعقارات
        self.properties_widget = PropertiesWidget(self.session)

        # إضافة التبويبات مع أيقونات مثل العمال
        self.tabs.addTab(projects_tab, "🏗️ بيانات المشاريع")
        self.tabs.addTab(self.properties_widget, "🏢 إدارة العقارات")

        # إضافة التبويبات إلى التخطيط الرئيسي
        main_layout.addWidget(self.tabs)

        self.setLayout(main_layout)

    def create_advanced_projects_table(self):
        """إنشاء جدول المشاريع المتطور والمحسن مطابق للموردين"""
        styled_table = StyledTable()
        self.projects_table = styled_table.table
        self.projects_table.setColumnCount(8)
        self.projects_table.setHorizontalHeaderLabels(["الرقم", "اسم المشروع", "العميل", "الموقع", "تاريخ البدء", "تاريخ الانتهاء المتوقع", "الحالة", "التقدم"])

        # تحسين عرض الأعمدة
        header = self.projects_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # الرقم
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم المشروع
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # العميل
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # الموقع
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # تاريخ البدء
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # تاريخ الانتهاء المتوقع
        header.setSectionResizeMode(6, QHeaderView.Fixed)  # الحالة
        header.setSectionResizeMode(7, QHeaderView.Fixed)  # التقدم

        # تحديد عرض الأعمدة الثابتة
        self.projects_table.setColumnWidth(0, 80)   # الرقم
        self.projects_table.setColumnWidth(4, 120)  # تاريخ البدء
        self.projects_table.setColumnWidth(5, 150)  # تاريخ الانتهاء المتوقع
        self.projects_table.setColumnWidth(6, 120)  # الحالة
        self.projects_table.setColumnWidth(7, 100)  # التقدم

        self.projects_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.projects_table.setSelectionMode(QTableWidget.SingleSelection)
        self.projects_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.projects_table.setAlternatingRowColors(True)

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للمصروفات والإيرادات والفواتير
        self.projects_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للمصروفات والإيرادات والفواتير
        header = self.projects_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                vertical-align: middle !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للمصروفات والإيرادات والفواتير
        self.projects_table.verticalHeader().setDefaultSectionSize(45)
        self.projects_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        # إضافة العلامة المائية للجدول مطابقة للفواتير
        self.add_watermark_to_projects_table()

    def update_total_label(self):
        """تحديث تسمية الإجمالي"""
        try:
            total_projects = self.table.rowCount()
            self.total_label.setText(f"إجمالي المشاريع: {total_projects}")
        except Exception as e:
            print(f"خطأ في تحديث إجمالي المشاريع: {str(e)}")

    def refresh_data(self):
        """تحديث بيانات المشاريع في الجدول"""
        # الحصول على جميع المشاريع من قاعدة البيانات
        projects = self.session.query(Project).order_by(Project.start_date.desc()).all()
        self.populate_table(projects)
        self.update_total_label()

    def populate_table(self, projects):
        """ملء جدول المشاريع بالبيانات"""
        self.projects_table.setRowCount(0)

        for row, project in enumerate(projects):
            self.projects_table.insertRow(row)

            # 1. الرقم مع أيقونة
            id_item = QTableWidgetItem(f"🔢 {project.id}")
            id_item.setTextAlignment(Qt.AlignCenter)
            id_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
            id_item.setForeground(QColor("#1e40af"))
            id_item.setToolTip(f"🔢 رقم المشروع: {project.id}")
            self.projects_table.setItem(row, 0, id_item)

            # 2. اسم المشروع مع أيقونة
            project_item = QTableWidgetItem(f"🏗️ {project.name}")
            project_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
            project_item.setForeground(QColor("#059669"))
            project_item.setToolTip(f"🏗️ اسم المشروع: {project.name}")
            self.projects_table.setItem(row, 1, project_item)

            # 3. اسم العميل مع أيقونة
            client_name = project.client.name if project.client else "غير محدد"
            client_item = QTableWidgetItem(f"👤 {client_name}")
            client_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
            client_item.setForeground(QColor("#7c3aed"))
            client_item.setToolTip(f"👤 العميل: {client_name}")
            self.projects_table.setItem(row, 2, client_item)

            # 4. الموقع مع أيقونة
            location = project.location or "غير محدد"
            location_item = QTableWidgetItem(f"📍 {location}")
            location_item.setFont(QFont("Segoe UI", 10, QFont.Normal))
            location_item.setForeground(QColor("#dc2626"))
            location_item.setToolTip(f"📍 الموقع: {location}")
            self.projects_table.setItem(row, 3, location_item)

            # 5. تاريخ البدء مع أيقونة
            start_date = project.start_date.strftime("%Y-%m-%d") if project.start_date else "غير محدد"
            start_date_item = QTableWidgetItem(f"🚀 {start_date}")
            start_date_item.setTextAlignment(Qt.AlignCenter)
            start_date_item.setFont(QFont("Segoe UI", 10, QFont.Normal))
            start_date_item.setForeground(QColor("#0891b2"))
            start_date_item.setToolTip(f"🚀 تاريخ البدء: {start_date}")
            self.projects_table.setItem(row, 4, start_date_item)

            # 6. تاريخ الانتهاء المتوقع مع أيقونة
            expected_end_date = project.expected_end_date.strftime("%Y-%m-%d") if project.expected_end_date else "غير محدد"
            end_date_item = QTableWidgetItem(f"🏁 {expected_end_date}")
            end_date_item.setTextAlignment(Qt.AlignCenter)
            end_date_item.setFont(QFont("Segoe UI", 10, QFont.Normal))
            end_date_item.setForeground(QColor("#ea580c"))
            end_date_item.setToolTip(f"🏁 تاريخ الانتهاء المتوقع: {expected_end_date}")
            self.projects_table.setItem(row, 5, end_date_item)

            # 7. حالة المشروع مع أيقونات ملونة
            status_icons = {
                'planning': '📋',
                'in_progress': '⚡',
                'completed': '✅',
                'cancelled': '❌'
            }
            status_map = {
                'planning': 'قيد التخطيط',
                'in_progress': 'قيد التنفيذ',
                'completed': 'مكتمل',
                'cancelled': 'ملغى'
            }
            status_icon = status_icons.get(project.status, '📂')
            status_text = status_map.get(project.status, project.status or "")
            status_item = QTableWidgetItem(f"{status_icon} {status_text}")
            status_item.setTextAlignment(Qt.AlignCenter)
            status_item.setFont(QFont("Segoe UI", 10, QFont.Bold))

            # تلوين الحالة مع ألوان محسنة
            if project.status == 'planning':
                status_item.setBackground(QColor(255, 255, 200))  # أصفر فاتح
                status_item.setForeground(QColor("#92400e"))
            elif project.status == 'in_progress':
                status_item.setBackground(QColor(200, 200, 255))  # أزرق فاتح
                status_item.setForeground(QColor("#1e40af"))
            elif project.status == 'completed':
                status_item.setBackground(QColor(200, 255, 200))  # أخضر فاتح
                status_item.setForeground(QColor("#059669"))
            elif project.status == 'cancelled':
                status_item.setBackground(QColor(255, 200, 200))  # أحمر فاتح
                status_item.setForeground(QColor("#dc2626"))

            status_item.setToolTip(f"{status_icon} حالة المشروع: {status_text}")
            self.projects_table.setItem(row, 6, status_item)

            # شريط التقدم
            progress = QProgressBar()
            if project.status == 'planning':
                progress.setValue(10)
            elif project.status == 'in_progress':
                progress.setValue(50)
            elif project.status == 'completed':
                progress.setValue(100)
            else:
                progress.setValue(0)

            self.projects_table.setCellWidget(row, 7, progress)

        # تحديث الإجمالي
        self.update_total_label()

    def add_watermark_to_projects_table(self):
        """إضافة علامة مائية لجدول المشاريع مطابقة للفواتير"""
        try:
            # إنشاء العلامة المائية مثل الفواتير
            watermark = QLabel("Smart Finish", self.projects_table)
            watermark.setAlignment(Qt.AlignCenter)

            # تنسيق العلامة المائية مكبرة أكثر للتوحيد مع الباقي
            font = QFont("Arial", 150, QFont.Bold)
            watermark.setFont(font)

            # تطبيق تصميم محسن للعلامة المائية - أكثر وضوحاً لكن لا يؤثر على البيانات
            watermark.setStyleSheet("""
                QLabel {
                    color: rgba(71, 85, 105, 0.35);
                    background: transparent;
                    border: none;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
                    font-weight: bold;
                }
            """)

            # إصلاح مشكلة التفاعل - العلامة المائية لا تتداخل مع النقرات
            watermark.setAttribute(Qt.WA_TransparentForMouseEvents, True)
            watermark.setEnabled(False)  # تعطيل التفاعل مع العلامة المائية
            watermark.lower()  # وضع العلامة المائية في الخلف

            # تحسين إضافي للشفافية والوضوح
            watermark.setWindowOpacity(0.6)  # شفافية محسنة

            # تحديد موضع وحجم العلامة المائية مع تحسين الموضع
            def update_watermark_geometry():
                if self.projects_table.isVisible():
                    rect = self.projects_table.rect()
                    # تحسين الموضع ليكون في المنتصف مع مساحة أفضل
                    watermark.setGeometry(rect.x() + 50, rect.y() + 100,
                                        rect.width() - 100, rect.height() - 200)
                    watermark.lower()  # وضع العلامة المائية في الخلف دائماً
                    watermark.setAttribute(Qt.WA_TransparentForMouseEvents, True)

            # ربط تحديث الموضع بتغيير حجم الجدول
            original_resize_event = self.projects_table.resizeEvent
            def custom_resize_event(event):
                original_resize_event(event)
                update_watermark_geometry()
            self.projects_table.resizeEvent = custom_resize_event

            # تحديث أولي للموضع
            update_watermark_geometry()

        except Exception as e:
            print(f"خطأ في إضافة العلامة المائية للمشاريع: {str(e)}")

    def filter_projects(self):
        """تصفية المشاريع بناءً على نص البحث والحالة"""
        search_text = self.search_edit.text().strip().lower()
        status = self.status_filter.currentData()

        # بناء الاستعلام
        query = self.session.query(Project)

        # تطبيق تصفية النص
        if search_text:
            query = query.filter(
                Project.name.like(f"%{search_text}%") |
                Project.location.like(f"%{search_text}%") |
                Project.description.like(f"%{search_text}%")
            )

        # تطبيق تصفية الحالة
        if status:
            query = query.filter(Project.status == status)

        # تنفيذ الاستعلام
        projects = query.order_by(Project.start_date.desc()).all()

        # تحديث الجدول
        self.populate_table(projects)

    def add_project(self):
        """إضافة مشروع جديد"""
        dialog = ProjectDialog(self, session=self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # إنشاء مشروع جديد
                project = Project(**data)
                self.session.add(project)
                self.session.commit()
                show_info_message("تم", "تم إضافة المشروع بنجاح")
                self.refresh_data()

    def edit_project(self):
        """تعديل مشروع"""
        selected_row = self.projects_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مشروع من القائمة")
            return

        project_id = int(self.projects_table.item(selected_row, 0).text())
        project = self.session.query(Project).get(project_id)

        if not project:
            show_error_message("خطأ", "لم يتم العثور على المشروع")
            return

        dialog = ProjectDialog(self, project, self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # تحديث بيانات المشروع
                for key, value in data.items():
                    setattr(project, key, value)

                self.session.commit()
                show_info_message("تم", "تم تحديث المشروع بنجاح")
                self.refresh_data()

    def delete_project(self):
        """حذف مشروع"""
        selected_row = self.projects_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مشروع من القائمة")
            return

        project_id = int(self.projects_table.item(selected_row, 0).text())
        project = self.session.query(Project).get(project_id)

        if not project:
            show_error_message("خطأ", "لم يتم العثور على المشروع")
            return

        # طلب تأكيد الحذف
        if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف المشروع '{project.name}'؟"):
            self.session.delete(project)
            self.session.commit()
            show_info_message("تم", "تم حذف المشروع بنجاح")
            self.refresh_data()

    def view_project(self):
        """عرض تفاصيل المشروع"""
        selected_row = self.projects_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مشروع من القائمة")
            return

        project_id = int(self.projects_table.item(selected_row, 0).text())
        project = self.session.query(Project).get(project_id)

        if not project:
            show_error_message("خطأ", "لم يتم العثور على المشروع")
            return

        # إنشاء نافذة لعرض تفاصيل المشروع
        dialog = QDialog(self)
        dialog.setWindowTitle(f"تفاصيل المشروع: {project.name}")
        dialog.setMinimumSize(600, 400)

        layout = QVBoxLayout()

        # عنوان المشروع
        title_label = QLabel(project.name)
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        layout.addWidget(title_label)

        # معلومات المشروع
        info_layout = QFormLayout()

        client_name = project.client.name if project.client else "غير محدد"
        info_layout.addRow("العميل:", QLabel(client_name))

        info_layout.addRow("الموقع:", QLabel(project.location or ""))

        area_text = f"{project.area} م²" if project.area else ""
        info_layout.addRow("المساحة:", QLabel(area_text))

        start_date = project.start_date.strftime("%Y-%m-%d") if project.start_date else ""
        info_layout.addRow("تاريخ البدء:", QLabel(start_date))

        expected_end_date = project.expected_end_date.strftime("%Y-%m-%d") if project.expected_end_date else ""
        info_layout.addRow("تاريخ الانتهاء المتوقع:", QLabel(expected_end_date))

        actual_end_date = project.actual_end_date.strftime("%Y-%m-%d") if project.actual_end_date else ""
        info_layout.addRow("تاريخ الانتهاء الفعلي:", QLabel(actual_end_date))

        status_map = {
            'planning': 'قيد التخطيط',
            'in_progress': 'قيد التنفيذ',
            'completed': 'مكتمل',
            'cancelled': 'ملغى'
        }
        status_text = status_map.get(project.status, project.status or "")
        info_layout.addRow("الحالة:", QLabel(status_text))

        budget_text = format_currency(project.budget) if project.budget else ""
        info_layout.addRow("الميزانية:", QLabel(budget_text))

        layout.addLayout(info_layout)

        # وصف المشروع
        if project.description:
            description_group = QGroupBox("وصف المشروع")
            description_layout = QVBoxLayout()
            description_label = QLabel(project.description)
            description_label.setWordWrap(True)
            description_layout.addWidget(description_label)
            description_group.setLayout(description_layout)
            layout.addWidget(description_group)

        # ملاحظات المشروع
        if project.notes:
            notes_group = QGroupBox("ملاحظات")
            notes_layout = QVBoxLayout()
            notes_label = QLabel(project.notes)
            notes_label.setWordWrap(True)
            notes_layout.addWidget(notes_label)
            notes_group.setLayout(notes_layout)
            layout.addWidget(notes_group)

        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #7f1d1d, stop:0.15 #991b1b, stop:0.85 #b91c1c, stop:1 #dc2626);
                color: #ffffff;
                border: 4px solid #dc2626;
                border-radius: 16px;
                padding: 8px 16px;
                font-weight: 900;
                font-size: 13px;
                min-height: 38px;
                max-height: 38px;
                min-width: 100px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                box-shadow: 0 6px 15px rgba(220, 38, 38, 0.5);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #991b1b, stop:0.15 #dc2626, stop:0.85 #ef4444, stop:1 #f87171);
                border: 4px solid #ef4444;
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(220, 38, 38, 0.5);
            }
        """)
        close_button.clicked.connect(dialog.accept)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)
        dialog.setLayout(layout)

        dialog.exec_()

    def manage_documents(self):
        """إدارة وثائق وصور المشروع"""
        selected_row = self.projects_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مشروع من القائمة")
            return

        project_id = int(self.projects_table.item(selected_row, 0).text())
        project = self.session.query(Project).get(project_id)

        if not project:
            show_error_message("خطأ", "لم يتم العثور على المشروع")
            return

        # إنشاء نافذة إدارة الوثائق
        dialog = QDialog(self)
        dialog.setWindowTitle(f"📁 إدارة وثائق المشروع: {project.name}")
        dialog.setModal(True)
        dialog.resize(600, 400)

        layout = QVBoxLayout()

        # معلومات المشروع
        info_label = QLabel(f"المشروع: {project.name}")
        info_label.setStyleSheet("font-weight: bold; font-size: 14px; padding: 10px; background-color: #f0f0f0; border-radius: 5px;")
        layout.addWidget(info_label)

        # قائمة الوثائق
        documents_list = QListWidget()
        documents_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:selected {
                background-color: #e3f2fd;
            }
        """)

        # إضافة وثائق تجريبية
        sample_docs = [
            "📄 مخطط المشروع.pdf",
            "📄 العقد الأساسي.docx",
            "🖼️ صورة الموقع.jpg",
            "📊 تقرير التقدم.xlsx",
            "📋 قائمة المهام.txt"
        ]
        for doc in sample_docs:
            item = QListWidgetItem(doc)
            documents_list.addItem(item)

        layout.addWidget(documents_list)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        add_doc_btn = QPushButton("📄 إضافة وثيقة")
        add_doc_btn.clicked.connect(lambda: (dialog.close(), self.add_document()))
        buttons_layout.addWidget(add_doc_btn)

        add_image_btn = QPushButton("🖼️ إضافة صورة")
        add_image_btn.clicked.connect(lambda: (dialog.close(), self.add_image()))
        buttons_layout.addWidget(add_image_btn)

        view_doc_btn = QPushButton("👁️ عرض الوثيقة")
        view_doc_btn.clicked.connect(lambda: self.view_selected_document(documents_list, project))
        buttons_layout.addWidget(view_doc_btn)

        delete_doc_btn = QPushButton("🗑️ حذف الوثيقة")
        delete_doc_btn.clicked.connect(lambda: self.delete_selected_document(documents_list, project))
        buttons_layout.addWidget(delete_doc_btn)

        export_btn = QPushButton("📤 تصدير الوثائق")
        export_btn.clicked.connect(lambda: (dialog.close(), self.export_documents()))
        buttons_layout.addWidget(export_btn)

        close_btn = QPushButton("❌ إغلاق")
        close_btn.clicked.connect(dialog.close)
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)
        dialog.setLayout(layout)
        dialog.exec_()

    def view_project_timeline(self):
        """عرض الجدول الزمني للمشروع"""
        selected_row = self.projects_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مشروع من القائمة")
            return

        project_id = int(self.projects_table.item(selected_row, 0).text())
        project = self.session.query(Project).get(project_id)

        if not project:
            show_error_message("خطأ", "لم يتم العثور على المشروع")
            return

        # إنشاء نافذة لعرض الجدول الزمني
        dialog = QDialog(self)
        dialog.setWindowTitle(f"الجدول الزمني - {project.name}")
        dialog.setMinimumSize(700, 500)

        layout = QVBoxLayout()

        # معلومات الجدول الزمني
        status_map = {'planning': 'قيد التخطيط', 'in_progress': 'قيد التنفيذ', 'completed': 'مكتمل', 'cancelled': 'ملغى'}
        progress_map = {'planning': '10%', 'in_progress': '50%', 'completed': '100%', 'cancelled': '0%'}

        timeline_text = f"""
📅 الجدول الزمني للمشروع - {project.name}

📋 التواريخ المهمة:
• تاريخ البدء: {project.start_date.strftime('%Y-%m-%d') if project.start_date else 'غير محدد'}
• تاريخ الانتهاء المتوقع: {project.expected_end_date.strftime('%Y-%m-%d') if project.expected_end_date else 'غير محدد'}
• تاريخ الانتهاء الفعلي: {project.actual_end_date.strftime('%Y-%m-%d') if project.actual_end_date else 'لم ينته بعد'}

⏱️ المدة الزمنية:
• المدة المخططة: {(project.expected_end_date - project.start_date).days if project.start_date and project.expected_end_date else 'غير محسوبة'} يوم
• المدة الفعلية: {(project.actual_end_date - project.start_date).days if project.start_date and project.actual_end_date else 'قيد التنفيذ'} يوم

📊 حالة المشروع:
• الحالة الحالية: {status_map.get(project.status, project.status or '')}
• نسبة الإنجاز: {progress_map.get(project.status, '0%')}

📝 ملاحظات الجدولة:
• تحقق من التقدم بانتظام
• راجع المواعيد النهائية
• تواصل مع العميل عند التأخير
        """

        timeline_label = QLabel(timeline_text)
        timeline_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 12px;")
        layout.addWidget(timeline_label)

        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.accept)
        layout.addWidget(close_button)

        dialog.setLayout(layout)
        dialog.exec_()

    def view_project_budget(self):
        """عرض تفاصيل الميزانية للمشروع"""
        selected_row = self.projects_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مشروع من القائمة")
            return

        project_id = int(self.projects_table.item(selected_row, 0).text())
        project = self.session.query(Project).get(project_id)

        if not project:
            show_error_message("خطأ", "لم يتم العثور على المشروع")
            return

        # إنشاء نافذة لعرض تفاصيل الميزانية
        dialog = QDialog(self)
        dialog.setWindowTitle(f"تفاصيل الميزانية - {project.name}")
        dialog.setMinimumSize(600, 400)

        layout = QVBoxLayout()

        # معلومات الميزانية
        budget_text = f"""
💰 تفاصيل الميزانية - {project.name}

💵 الميزانية المخططة:
• إجمالي الميزانية: {format_currency(project.budget or 0)}
• الميزانية لكل متر مربع: {format_currency((project.budget or 0) / (project.area or 1)) if project.area else 'غير محسوبة'}

📊 تحليل التكاليف:
• تكلفة المواد (تقديرية): {format_currency((project.budget or 0) * 0.6)}
• تكلفة العمالة (تقديرية): {format_currency((project.budget or 0) * 0.3)}
• تكاليف أخرى (تقديرية): {format_currency((project.budget or 0) * 0.1)}

📈 مؤشرات مالية:
• حالة الميزانية: {'مناسبة' if (project.budget or 0) > 0 else 'غير محددة'}
• نوع المشروع: {'اقتصادي' if (project.budget or 0) < 100000 else 'متوسط' if (project.budget or 0) < 500000 else 'كبير'}

📝 ملاحظات مالية:
• راجع الميزانية بانتظام
• احتفظ بهامش للطوارئ (10-15%)
• وثق جميع المصروفات
        """

        budget_label = QLabel(budget_text)
        budget_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 12px;")
        layout.addWidget(budget_label)

        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.accept)
        layout.addWidget(close_button)

        dialog.setLayout(layout)
        dialog.exec_()

    def view_project_progress(self):
        """عرض تقرير التقدم للمشروع"""
        selected_row = self.projects_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مشروع من القائمة")
            return

        project_id = int(self.projects_table.item(selected_row, 0).text())
        project = self.session.query(Project).get(project_id)

        if not project:
            show_error_message("خطأ", "لم يتم العثور على المشروع")
            return

        # إنشاء نافذة لعرض تقرير التقدم
        dialog = QDialog(self)
        dialog.setWindowTitle(f"تقرير التقدم - {project.name}")
        dialog.setMinimumSize(650, 450)

        layout = QVBoxLayout()

        # معلومات التقدم
        progress_percentage = {'planning': 10, 'in_progress': 50, 'completed': 100, 'cancelled': 0}.get(project.status, 0)
        status_map = {'planning': 'قيد التخطيط', 'in_progress': 'قيد التنفيذ', 'completed': 'مكتمل', 'cancelled': 'ملغى'}

        progress_text = f"""
📊 تقرير التقدم - {project.name}

🎯 حالة المشروع:
• الحالة: {status_map.get(project.status, project.status or '')}
• نسبة الإنجاز: {progress_percentage}%
• العميل: {project.client.name if project.client else 'غير محدد'}

📅 التقدم الزمني:
• أيام منذ البدء: {(datetime.datetime.now().date() - project.start_date.date()).days if project.start_date else 'غير محسوب'}
• أيام متبقية: {(project.expected_end_date.date() - datetime.datetime.now().date()).days if project.expected_end_date and project.expected_end_date > datetime.datetime.now() else 'انتهت المدة' if project.expected_end_date else 'غير محسوب'}

📍 معلومات الموقع:
• الموقع: {project.location or 'غير محدد'}
• المساحة: {project.area or 0} متر مربع

💰 المعلومات المالية:
• الميزانية: {format_currency(project.budget or 0)}
• التكلفة المتوقعة حتى الآن: {format_currency((project.budget or 0) * (progress_percentage / 100))}

📈 مؤشرات الأداء:
• سرعة التنفيذ: {'بطيئة' if progress_percentage < 30 else 'متوسطة' if progress_percentage < 70 else 'سريعة'}
• حالة الجدولة: {'متأخر' if project.expected_end_date and project.expected_end_date < datetime.datetime.now() and project.status != 'completed' else 'في الموعد'}

📝 التوصيات:
• {'تسريع وتيرة العمل' if progress_percentage < 50 else 'الحفاظ على الوتيرة الحالية'}
• {'مراجعة الجدولة' if project.expected_end_date and project.expected_end_date < datetime.datetime.now() else 'متابعة الخطة'}
        """

        progress_label = QLabel(progress_text)
        progress_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 12px;")
        layout.addWidget(progress_label)

        # شريط التقدم البصري
        progress_bar = QProgressBar()
        progress_bar.setValue(progress_percentage)
        progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                font-size: 14px;
                height: 25px;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 6px;
            }
        """)
        layout.addWidget(progress_bar)

        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.accept)
        layout.addWidget(close_button)

        dialog.setLayout(layout)
        dialog.exec_()

    def add_document(self):
        """إضافة وثيقة للمشروع"""
        selected_row = self.projects_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مشروع من القائمة")
            return

        project_id = int(self.projects_table.item(selected_row, 0).text())
        project = self.session.query(Project).get(project_id)

        if not project:
            show_error_message("خطأ", "لم يتم العثور على المشروع")
            return

        # فتح نافذة اختيار الملف
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختيار وثيقة للإضافة",
            "",
            "جميع الملفات (*);;ملفات PDF (*.pdf);;ملفات Word (*.docx *.doc);;ملفات Excel (*.xlsx *.xls);;ملفات نصية (*.txt)"
        )

        if file_path:
            # إنشاء نافذة تأكيد مع تفاصيل الملف
            import os
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            file_size_mb = file_size / (1024 * 1024)

            dialog = QDialog(self)
            dialog.setWindowTitle("📄 تأكيد إضافة الوثيقة")
            dialog.setModal(True)
            dialog.resize(500, 300)

            layout = QVBoxLayout()

            # معلومات الملف
            info_text = f"""
📁 تفاصيل الوثيقة:
─────────────────────────────────────────────────────────────────────────────
🏗️ المشروع: {project.name}
📄 اسم الملف: {file_name}
📊 حجم الملف: {file_size_mb:.2f} ميجابايت
📍 المسار: {file_path}

هل تريد إضافة هذه الوثيقة للمشروع؟
            """

            info_label = QLabel(info_text)
            info_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 11px;")
            layout.addWidget(info_label)

            # أزرار التأكيد
            buttons_layout = QHBoxLayout()

            confirm_btn = QPushButton("✅ إضافة الوثيقة")
            confirm_btn.clicked.connect(lambda: self.confirm_add_document(dialog, project, file_path, file_name))
            buttons_layout.addWidget(confirm_btn)

            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.clicked.connect(dialog.close)
            buttons_layout.addWidget(cancel_btn)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

    def confirm_add_document(self, dialog, project, file_path, file_name):
        """تأكيد إضافة الوثيقة"""
        try:
            # في التطبيق الحقيقي، ستتم إضافة الوثيقة لقاعدة البيانات
            # هنا سنعرض رسالة نجاح فقط

            dialog.close()
            show_info_message(
                "تم بنجاح",
                f"تم إضافة الوثيقة بنجاح:\n\n📄 {file_name}\n🏗️ للمشروع: {project.name}\n\nملاحظة: في النسخة المكتملة ستتم إضافة الوثيقة فعلياً لقاعدة البيانات."
            )

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إضافة الوثيقة: {str(e)}")

    def add_image(self):
        """إضافة صورة للمشروع"""
        selected_row = self.projects_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مشروع من القائمة")
            return

        project_id = int(self.projects_table.item(selected_row, 0).text())
        project = self.session.query(Project).get(project_id)

        if not project:
            show_error_message("خطأ", "لم يتم العثور على المشروع")
            return

        # فتح نافذة اختيار الصورة
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختيار صورة للإضافة",
            "",
            "ملفات الصور (*.jpg *.jpeg *.png *.bmp *.gif);;جميع الملفات (*)"
        )

        if file_path:
            # إنشاء نافذة معاينة الصورة
            import os
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            file_size_mb = file_size / (1024 * 1024)

            dialog = QDialog(self)
            dialog.setWindowTitle("🖼️ معاينة وإضافة الصورة")
            dialog.setModal(True)
            dialog.resize(600, 500)

            layout = QVBoxLayout()

            # معلومات الصورة
            info_text = f"""
🖼️ تفاصيل الصورة:
─────────────────────────────────────────────────────────────────────────────
🏗️ المشروع: {project.name}
📷 اسم الصورة: {file_name}
📊 حجم الملف: {file_size_mb:.2f} ميجابايت
📍 المسار: {file_path}
            """

            info_label = QLabel(info_text)
            info_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 11px;")
            layout.addWidget(info_label)

            # معاينة الصورة
            try:
                from PyQt5.QtGui import QPixmap
                preview_label = QLabel()
                pixmap = QPixmap(file_path)
                if not pixmap.isNull():
                    # تصغير الصورة للمعاينة
                    scaled_pixmap = pixmap.scaled(300, 200, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    preview_label.setPixmap(scaled_pixmap)
                    preview_label.setAlignment(Qt.AlignCenter)
                    preview_label.setStyleSheet("border: 2px solid #ddd; border-radius: 8px; padding: 10px;")
                else:
                    preview_label.setText("❌ لا يمكن معاينة الصورة")
                    preview_label.setAlignment(Qt.AlignCenter)

                layout.addWidget(preview_label)

            except Exception as e:
                error_label = QLabel(f"❌ خطأ في معاينة الصورة: {str(e)}")
                error_label.setAlignment(Qt.AlignCenter)
                layout.addWidget(error_label)

            # أزرار التأكيد
            buttons_layout = QHBoxLayout()

            confirm_btn = QPushButton("✅ إضافة الصورة")
            confirm_btn.clicked.connect(lambda: self.confirm_add_image(dialog, project, file_path, file_name))
            buttons_layout.addWidget(confirm_btn)

            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.clicked.connect(dialog.close)
            buttons_layout.addWidget(cancel_btn)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

    def confirm_add_image(self, dialog, project, file_path, file_name):
        """تأكيد إضافة الصورة"""
        try:
            # في التطبيق الحقيقي، ستتم إضافة الصورة لقاعدة البيانات
            # هنا سنعرض رسالة نجاح فقط

            dialog.close()
            show_info_message(
                "تم بنجاح",
                f"تم إضافة الصورة بنجاح:\n\n🖼️ {file_name}\n🏗️ للمشروع: {project.name}\n\nملاحظة: في النسخة المكتملة ستتم إضافة الصورة فعلياً لقاعدة البيانات."
            )

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إضافة الصورة: {str(e)}")

    def view_image_gallery(self):
        """عرض معرض صور المشروع"""
        selected_row = self.projects_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار مشروع من القائمة")
            return

        project_id = int(self.projects_table.item(selected_row, 0).text())
        project = self.session.query(Project).get(project_id)

        if not project:
            show_error_message("خطأ", "لم يتم العثور على المشروع")
            return

        # إنشاء نافذة معرض الصور
        dialog = QDialog(self)
        dialog.setWindowTitle(f"🖼️ معرض صور المشروع: {project.name}")
        dialog.setModal(True)
        dialog.resize(800, 600)

        layout = QVBoxLayout()

        # معلومات المشروع
        info_label = QLabel(f"🏗️ المشروع: {project.name}")
        info_label.setStyleSheet("font-weight: bold; font-size: 14px; padding: 10px; background-color: #f0f0f0; border-radius: 5px;")
        layout.addWidget(info_label)

        # شبكة الصور التجريبية
        from PyQt5.QtWidgets import QGridLayout, QScrollArea

        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        grid_layout = QGridLayout()

        # إضافة صور تجريبية
        sample_images = [
            ("🖼️ صورة الموقع", "صورة عامة للموقع"),
            ("📷 بداية العمل", "صورة بداية المشروع"),
            ("🏗️ مرحلة البناء", "صورة أثناء التنفيذ"),
            ("✅ النتيجة النهائية", "صورة المشروع المكتمل"),
            ("📋 المخططات", "صورة المخططات"),
            ("🔧 الأدوات", "صورة الأدوات المستخدمة")
        ]

        row = 0
        col = 0
        for title, description in sample_images:
            # إنشاء بطاقة للصورة
            image_card = QFrame()
            image_card.setStyleSheet("""
                QFrame {
                    border: 2px solid #ddd;
                    border-radius: 10px;
                    padding: 10px;
                    background-color: #f9f9f9;
                }
                QFrame:hover {
                    border-color: #007bff;
                    background-color: #e3f2fd;
                }
            """)
            image_card.setFixedSize(200, 150)

            card_layout = QVBoxLayout()

            # أيقونة الصورة
            icon_label = QLabel("🖼️")
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setStyleSheet("font-size: 48px; color: #666;")
            card_layout.addWidget(icon_label)

            # عنوان الصورة
            title_label = QLabel(title)
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setStyleSheet("font-weight: bold; font-size: 12px;")
            card_layout.addWidget(title_label)

            # وصف الصورة
            desc_label = QLabel(description)
            desc_label.setAlignment(Qt.AlignCenter)
            desc_label.setStyleSheet("font-size: 10px; color: #666;")
            desc_label.setWordWrap(True)
            card_layout.addWidget(desc_label)

            image_card.setLayout(card_layout)
            grid_layout.addWidget(image_card, row, col)

            col += 1
            if col >= 3:  # 3 صور في كل صف
                col = 0
                row += 1

        scroll_widget.setLayout(grid_layout)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        add_image_btn = QPushButton("📷 إضافة صورة جديدة")
        add_image_btn.clicked.connect(lambda: (dialog.close(), self.add_image()))
        buttons_layout.addWidget(add_image_btn)

        export_btn = QPushButton("📤 تصدير الصور")
        export_btn.clicked.connect(lambda: show_info_message("قريباً", "ميزة تصدير الصور ستكون متاحة قريباً!"))
        buttons_layout.addWidget(export_btn)

        close_btn = QPushButton("❌ إغلاق")
        close_btn.clicked.connect(dialog.close)
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)
        dialog.setLayout(layout)
        dialog.exec_()

    def generate_projects_report(self):
        """إنشاء تقرير المشاريع مطابق للأقسام الأخرى"""
        try:
            projects = self.session.query(Project).all()

            if not projects:
                show_info_message("تقرير المشاريع", "لا توجد مشاريع لإنشاء التقرير")
                return

            # إنشاء محتوى التقرير
            report_content = self.generate_projects_report_content()

            # عرض التقرير في نافذة مطابقة للأقسام الأخرى
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextBrowser, QPushButton, QHBoxLayout

            dialog = QDialog(self)
            dialog.setWindowTitle("📋 تقرير المشاريع")
            dialog.setModal(True)
            dialog.resize(800, 600)

            layout = QVBoxLayout()

            text_browser = QTextBrowser()
            text_browser.setPlainText(report_content)
            text_browser.setStyleSheet("""
                QTextBrowser {
                    font-family: 'Segoe UI', Arial, sans-serif;
                    font-size: 11px;
                    line-height: 1.4;
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 15px;
                }
            """)
            layout.addWidget(text_browser)

            # أزرار الحفظ والطباعة
            buttons_layout = QHBoxLayout()

            save_button = QPushButton("💾 حفظ التقرير")
            self.style_advanced_button(save_button, 'emerald')
            save_button.clicked.connect(lambda: self.save_report_to_file(report_content, "تقرير_المشاريع"))
            buttons_layout.addWidget(save_button)

            print_button = QPushButton("🖨️ طباعة")
            self.style_advanced_button(print_button, 'info')
            print_button.clicked.connect(lambda: self.print_report(text_browser))
            buttons_layout.addWidget(print_button)

            close_button = QPushButton("❌ إغلاق")
            self.style_advanced_button(close_button, 'danger')
            close_button.clicked.connect(dialog.close)
            buttons_layout.addWidget(close_button)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إنشاء التقرير: {str(e)}")

    def generate_projects_report_content(self):
        """إنشاء محتوى تقرير المشاريع"""
        try:
            from datetime import datetime

            projects = self.session.query(Project).all()

            # حساب الإحصائيات
            total_projects = len(projects)
            total_budget = sum(project.budget or 0 for project in projects)

            # إحصائيات حسب الحالة
            status_stats = {}
            for project in projects:
                status = project.status or 'غير محدد'
                if status in status_stats:
                    status_stats[status] += 1
                else:
                    status_stats[status] = 1

            # إنشاء محتوى التقرير
            report_content = f"""
═══════════════════════════════════════════════════════════════════════════════
                                📋 تقرير مفصل للمشاريع
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d')}
⏰ وقت الإنشاء: {datetime.now().strftime('%H:%M:%S')}

📊 ملخص عام:
─────────────────────────────────────────────────────────────────────────────
🏗️ إجمالي المشاريع: {total_projects}
💰 إجمالي الميزانيات: {int(total_budget):,} جنيه

📈 تفصيل حسب الحالة:
─────────────────────────────────────────────────────────────────────────────
"""

            for status, count in status_stats.items():
                percentage = (count / total_projects) * 100
                report_content += f"• {status}: {count} مشروع ({percentage:.1f}%)\n"

            report_content += f"""
─────────────────────────────────────────────────────────────────────────────

📋 تفاصيل المشاريع:
═══════════════════════════════════════════════════════════════════════════════
"""

            for project in projects:
                client_name = project.client.name if project.client else "غير محدد"
                start_date = project.start_date.strftime("%Y-%m-%d") if project.start_date else "غير محدد"
                budget = int(project.budget) if project.budget else 0

                report_content += f"""
🔸 {project.name}
   👤 العميل: {client_name}
   📅 تاريخ البدء: {start_date}
   📊 الحالة: {project.status or 'غير محدد'}
   💰 الميزانية: {budget:,} جنيه
   📝 الوصف: {project.description or 'لا يوجد وصف'}
   ─────────────────────────────────────────────────────────────────────────────
"""

            return report_content

        except Exception as e:
            return f"خطأ في إنشاء محتوى التقرير: {str(e)}"

    def save_report_to_file(self, content, filename):
        """حفظ التقرير إلى ملف نصي"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير", f"{filename}.txt", "Text Files (*.txt)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                show_info_message("تم", f"تم حفظ التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في حفظ التقرير: {str(e)}")

    def print_report(self, text_browser):
        """طباعة التقرير"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

            printer = QPrinter()
            print_dialog = QPrintDialog(printer, self)

            if print_dialog.exec_() == QPrintDialog.Accepted:
                text_browser.print_(printer)
                show_info_message("تم", "تم إرسال التقرير للطباعة بنجاح")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في الطباعة: {str(e)}")

    def show_statistics(self):
        """عرض إحصائيات المشاريع"""
        try:
            projects = self.session.query(Project).all()

            if not projects:
                show_info_message("إحصائيات المشاريع", "لا توجد مشاريع لعرض الإحصائيات")
                return

            # حساب الإحصائيات
            total_projects = len(projects)
            total_budget = sum(project.budget or 0 for project in projects)
            avg_budget = total_budget / total_projects if total_projects > 0 else 0

            # إحصائيات حسب الحالة
            status_stats = {}
            for project in projects:
                status = project.status or 'غير محدد'
                if status in status_stats:
                    status_stats[status] += 1
                else:
                    status_stats[status] = 1

            # إحصائيات حسب العملاء
            client_stats = {}
            for project in projects:
                client_name = project.client.name if project.client else 'غير محدد'
                if client_name in client_stats:
                    client_stats[client_name] += 1
                else:
                    client_stats[client_name] = 1

            # إنشاء نافذة الإحصائيات
            dialog = QDialog(self)
            dialog.setWindowTitle("📊 إحصائيات المشاريع")
            dialog.setModal(True)
            dialog.resize(600, 500)

            layout = QVBoxLayout()

            # الإحصائيات العامة
            general_stats = f"""
📊 الإحصائيات العامة:
─────────────────────────────────────────────────────────────────────────────
🏗️ إجمالي المشاريع: {total_projects}
💰 إجمالي الميزانيات: {int(total_budget):,} جنيه
📊 متوسط الميزانية: {int(avg_budget):,} جنيه

📈 توزيع المشاريع حسب الحالة:
─────────────────────────────────────────────────────────────────────────────
"""

            for status, count in status_stats.items():
                percentage = (count / total_projects) * 100
                general_stats += f"• {status}: {count} مشروع ({percentage:.1f}%)\n"

            general_stats += f"""
─────────────────────────────────────────────────────────────────────────────

👥 توزيع المشاريع حسب العملاء:
─────────────────────────────────────────────────────────────────────────────
"""

            # أفضل 5 عملاء
            sorted_clients = sorted(client_stats.items(), key=lambda x: x[1], reverse=True)[:5]
            for client, count in sorted_clients:
                percentage = (count / total_projects) * 100
                general_stats += f"• {client}: {count} مشروع ({percentage:.1f}%)\n"

            # عرض الإحصائيات
            stats_text = QTextBrowser()
            stats_text.setPlainText(general_stats)
            stats_text.setStyleSheet("""
                QTextBrowser {
                    font-family: 'Segoe UI', Arial, sans-serif;
                    font-size: 11px;
                    line-height: 1.4;
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 15px;
                }
            """)
            layout.addWidget(stats_text)

            # زر إغلاق
            close_button = QPushButton("❌ إغلاق")
            close_button.clicked.connect(dialog.close)
            layout.addWidget(close_button)

            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في عرض الإحصائيات: {str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة مطابق للفواتير"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق للفواتير
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#047857', 'hover_mid': '#059669', 'hover_end': '#10b981', 'hover_bottom': '#34d399',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#052e16', 'pressed_border': '#064e3b',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.5)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#991b1b', 'hover_mid': '#dc2626', 'hover_end': '#ef4444', 'hover_bottom': '#f87171',
                    'hover_border': '#ef4444', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.5)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0284c7', 'bg_bottom': '#0ea5e9',
                    'hover_start': '#075985', 'hover_mid': '#0891b2', 'hover_end': '#0ea5e9', 'hover_bottom': '#38bdf8',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0284c7', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.5)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#14b8a6',
                    'hover_start': '#134e4a', 'hover_mid': '#0d9488', 'hover_end': '#14b8a6', 'hover_bottom': '#2dd4bf',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.5)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#06b6d4',
                    'hover_start': '#164e63', 'hover_mid': '#0891b2', 'hover_end': '#06b6d4', 'hover_bottom': '#22d3ee',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.5)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#ec4899',
                    'hover_start': '#831843', 'hover_mid': '#be185d', 'hover_end': '#ec4899', 'hover_bottom': '#f472b6',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.5)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#6366f1',
                    'hover_start': '#312e81', 'hover_mid': '#4f46e5', 'hover_end': '#6366f1', 'hover_bottom': '#818cf8',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4f46e5', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.5)'
                },
                'orange': {
                    'bg_start': '#431407', 'bg_mid': '#7c2d12', 'bg_end': '#9a3412', 'bg_bottom': '#f97316',
                    'hover_start': '#7c2d12', 'hover_mid': '#c2410c', 'hover_end': '#f97316', 'hover_bottom': '#fb923c',
                    'hover_border': '#f97316', 'pressed_start': '#431407', 'pressed_mid': '#7c2d12',
                    'pressed_end': '#9a3412', 'pressed_bottom': '#c2410c', 'pressed_border': '#9a3412',
                    'border': '#f97316', 'text': '#ffffff', 'shadow': 'rgba(249, 115, 22, 0.5)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات - مطابق للفواتير
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']},
                               0 0 30px rgba(255, 255, 255, 0.1);
                    letter-spacing: 0.3px;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 4px solid {color_scheme['hover_border']};
                    transform: translateY(-2px);
                    box-shadow: 0 8px 20px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 25px {color_scheme['shadow']},
                               0 0 40px rgba(255, 255, 255, 0.15);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px);
                    box-shadow: 0 3px 8px {color_scheme['shadow']},
                               inset 0 1px 0 rgba(255, 255, 255, 0.2),
                               inset 0 -1px 0 rgba(0, 0, 0, 0.5),
                               0 0 15px {color_scheme['shadow']};
                }}
                QPushButton::menu-indicator {{
                    {f"image: none; width: 0px;" if not has_menu else "width: 12px; height: 12px; margin-right: 4px;"}
                }}
            """

            button.setStyleSheet(style)

        except Exception as e:
            print(f"❌ خطأ في تطبيق تصميم الزر المتطور: {str(e)}")

    def export_to_excel(self):
        """تصدير المشاريع إلى Excel"""
        self.export_to_csv()  # نفس الوظيفة

    def export_to_csv(self):
        """تصدير المشاريع إلى CSV"""
        try:
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف CSV", "المشاريع.csv", "CSV Files (*.csv)"
            )

            if file_path:
                projects = self.session.query(Project).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة رؤوس الأعمدة
                    writer.writerow(['الرقم', 'اسم المشروع', 'العميل', 'التاريخ', 'الحالة', 'الميزانية', 'الوصف'])

                    # كتابة البيانات
                    for project in projects:
                        client_name = project.client.name if project.client else "غير محدد"
                        date_str = project.start_date.strftime("%Y-%m-%d") if project.start_date else ""

                        writer.writerow([
                            project.id,
                            project.name,
                            client_name,
                            date_str,
                            project.status or "",
                            project.budget or 0,
                            project.description or ""
                        ])

                show_info_message("تم", f"تم تصدير المشاريع بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير: {str(e)}")

    def export_to_pdf(self):
        """تصدير المشاريع إلى PDF"""
        try:
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QTextDocument

            projects = self.session.query(Project).all()

            if not projects:
                show_info_message("تصدير PDF", "لا توجد مشاريع للتصدير")
                return

            # حفظ ملف PDF مباشرة
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير المشاريع", "تقرير_المشاريع.pdf", "PDF Files (*.pdf)"
            )

            if file_path:
                # إنشاء محتوى HTML
                html_content = f"""
                <html dir="rtl">
                <head>
                    <meta charset="utf-8">
                    <title>تقرير المشاريع</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 20px; }}
                        h1 {{ color: #2563eb; text-align: center; }}
                        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                        th {{ background-color: #f2f2f2; }}
                    </style>
                </head>
                <body>
                    <h1>🏗️ تقرير المشاريع</h1>
                    <p><strong>تاريخ التقرير:</strong> {QDate.currentDate().toString('yyyy-MM-dd')}</p>

                    <table>
                        <tr>
                            <th>الرقم</th>
                            <th>اسم المشروع</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>الحالة</th>
                            <th>الميزانية</th>
                        </tr>
                """

                for project in projects:
                    client_name = project.client.name if project.client else "غير محدد"
                    date_str = project.start_date.strftime("%Y-%m-%d") if project.start_date else ""
                    budget = int(project.budget) if project.budget else 0

                    html_content += f"""
                        <tr>
                            <td>{project.id}</td>
                            <td>{project.name}</td>
                            <td>{client_name}</td>
                            <td>{date_str}</td>
                            <td>{project.status or ""}</td>
                            <td>{budget:,} جنيه</td>
                        </tr>
                    """

                html_content += """
                    </table>
                </body>
                </html>
                """

                # إنشاء طابعة PDF
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)
                printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

                # إنشاء مستند وطباعته إلى PDF
                document = QTextDocument()
                document.setHtml(html_content)
                document.print_(printer)

                show_info_message("تم", f"تم تصدير المشاريع إلى PDF بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تصدير PDF: {str(e)}")

    def export_to_json(self):
        """تصدير المشاريع إلى JSON"""
        try:
            import json

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف JSON", "المشاريع.json", "JSON Files (*.json)"
            )

            if file_path:
                projects = self.session.query(Project).all()

                projects_data = []
                for project in projects:
                    project_data = {
                        'id': project.id,
                        'name': project.name,
                        'client': project.client.name if project.client else None,
                        'start_date': project.start_date.strftime("%Y-%m-%d") if project.start_date else None,
                        'status': project.status,
                        'budget': float(project.budget) if project.budget else 0,
                        'description': project.description
                    }
                    projects_data.append(project_data)

                export_data = {
                    "export_info": {
                        "export_date": QDate.currentDate().toString('yyyy-MM-dd'),
                        "total_projects": len(projects_data),
                        "exported_by": "نظام إدارة المشاريع"
                    },
                    "projects": projects_data
                }

                with open(file_path, 'w', encoding='utf-8') as jsonfile:
                    json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)

                show_info_message("تم", f"تم تصدير المشاريع بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير: {str(e)}")

    def export_documents(self):
        """تصدير قائمة الوثائق"""
        selected_row = self.projects_table.currentRow()
        if selected_row < 0:
            show_info_message("تصدير الوثائق", "الرجاء اختيار مشروع لتصدير وثائقه")
            return

        project_id = int(self.projects_table.item(selected_row, 0).text())
        project = self.session.query(Project).get(project_id)

        if not project:
            show_error_message("خطأ", "لم يتم العثور على المشروع")
            return

        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ قائمة الوثائق", f"وثائق_{project.name}.txt", "Text Files (*.txt)"
            )

            if file_path:
                # إنشاء قائمة الوثائق
                documents_content = f"""
═══════════════════════════════════════════════════════════════════════════════
                            📁 قائمة وثائق المشروع
═══════════════════════════════════════════════════════════════════════════════

🏗️ اسم المشروع: {project.name}
👤 العميل: {project.client.name if project.client else 'غير محدد'}
📅 تاريخ التصدير: {QDate.currentDate().toString('yyyy-MM-dd')}

📋 الوثائق المتوفرة:
─────────────────────────────────────────────────────────────────────────────
📄 مخطط المشروع.pdf
📄 العقد الأساسي.docx
🖼️ صورة الموقع.jpg
📊 تقرير التقدم.xlsx
📋 قائمة المهام.txt
📝 ملاحظات المشروع.docx
💰 تفاصيل الميزانية.xlsx

📊 ملخص:
─────────────────────────────────────────────────────────────────────────────
• إجمالي الوثائق: 7
• الوثائق النصية: 4
• الصور: 1
• جداول البيانات: 2

ملاحظة: هذه قائمة تجريبية للوثائق. في النسخة المكتملة ستعرض الوثائق الفعلية المرفوعة.
"""

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(documents_content)

                show_info_message("تم", f"تم تصدير قائمة الوثائق بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تصدير الوثائق: {str(e)}")

    def view_selected_document(self, documents_list, project):
        """عرض تفاصيل الوثيقة المحددة"""
        current_item = documents_list.currentItem()
        if not current_item:
            show_info_message("تنبيه", "الرجاء اختيار وثيقة من القائمة")
            return

        document_name = current_item.text()

        # إنشاء نافذة تفاصيل الوثيقة
        dialog = QDialog(self)
        dialog.setWindowTitle(f"👁️ تفاصيل الوثيقة")
        dialog.setModal(True)
        dialog.resize(500, 400)

        layout = QVBoxLayout()

        # تفاصيل الوثيقة
        details_text = f"""
📄 تفاصيل الوثيقة:
─────────────────────────────────────────────────────────────────────────────
🏗️ المشروع: {project.name}
📁 اسم الوثيقة: {document_name}
📅 تاريخ الإضافة: {QDate.currentDate().toString('yyyy-MM-dd')}
👤 أضيفت بواسطة: المستخدم الحالي
📊 حجم الملف: 2.5 ميجابايت (تجريبي)
🔗 النوع: {document_name.split('.')[-1] if '.' in document_name else 'غير محدد'}

📝 الوصف:
هذه وثيقة تجريبية مرتبطة بالمشروع. في النسخة المكتملة ستعرض التفاصيل الحقيقية للوثيقة.

🔧 الإجراءات المتاحة:
• عرض الوثيقة
• تحميل الوثيقة
• تعديل تفاصيل الوثيقة
• حذف الوثيقة
        """

        details_label = QLabel(details_text)
        details_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 11px;")
        layout.addWidget(details_label)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        open_btn = QPushButton("📂 فتح الوثيقة")
        open_btn.clicked.connect(lambda: show_info_message("قريباً", "ميزة فتح الوثيقة ستكون متاحة قريباً!"))
        buttons_layout.addWidget(open_btn)

        download_btn = QPushButton("⬇️ تحميل")
        download_btn.clicked.connect(lambda: show_info_message("قريباً", "ميزة تحميل الوثيقة ستكون متاحة قريباً!"))
        buttons_layout.addWidget(download_btn)

        close_btn = QPushButton("❌ إغلاق")
        close_btn.clicked.connect(dialog.close)
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)
        dialog.setLayout(layout)
        dialog.exec_()

    def delete_selected_document(self, documents_list, project):
        """حذف الوثيقة المحددة"""
        current_item = documents_list.currentItem()
        if not current_item:
            show_info_message("تنبيه", "الرجاء اختيار وثيقة من القائمة")
            return

        document_name = current_item.text()

        # تأكيد الحذف
        from utils import show_confirmation_message
        if show_confirmation_message(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف الوثيقة:\n\n📄 {document_name}\n\nلا يمكن التراجع عن هذا الإجراء!"
        ):
            # حذف العنصر من القائمة
            row = documents_list.row(current_item)
            documents_list.takeItem(row)

            show_info_message(
                "تم الحذف",
                f"تم حذف الوثيقة بنجاح:\n\n📄 {document_name}\n\nملاحظة: في النسخة المكتملة ستتم إزالة الوثيقة من قاعدة البيانات أيضاً."
            )
