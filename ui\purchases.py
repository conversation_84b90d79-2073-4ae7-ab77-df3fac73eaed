from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime
import sys

class PurchasesWidget(QWidget):
    """واجهة إدارة المشتريات"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()
        self.create_sample_data()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(8)

        # العنوان الرئيسي مطابق للفواتير
        title_label = QLabel("🛒 إدارة المشتريات المتطورة - نظام شامل ومتقدم لإدارة المشتريات مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إطار البحث
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(10, 10, 10, 10)
        search_layout.setSpacing(10)

        # البحث مطابق للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث بالمورد، رقم الفاتورة أو المنتج...")
        self.search_edit.textChanged.connect(self.filter_purchases)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 3px solid rgba(139, 92, 246, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(240, 249, 255, 0.9),
                    stop:1 rgba(224, 242, 254, 0.85));
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QLineEdit:hover {
                border: 3px solid rgba(124, 58, 237, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
            }
        """)

        # فلتر الحالة مطابق للفواتير
        status_label = QLabel("📊 حالة:")
        status_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        status_label.setAlignment(Qt.AlignCenter)

        self.status_filter = QComboBox()
        self.status_filter.addItems(["جميع الحالات", "مكتملة", "معلقة", "ملغية"])
        self.status_filter.currentIndexChanged.connect(self.filter_purchases)
        self.status_filter.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                min-width: 120px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QComboBox:hover {
                border: 3px solid rgba(124, 58, 237, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
            }
            QComboBox:focus {
                border: 3px solid rgba(139, 92, 246, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(240, 249, 255, 0.9),
                    stop:1 rgba(224, 242, 254, 0.85));
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid rgba(96, 165, 250, 0.8);
                width: 8px;
                height: 8px;
                border-radius: 4px;
                background: rgba(96, 165, 250, 0.8);
            }
        """)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_edit, 1)
        search_layout.addWidget(status_label)
        search_layout.addWidget(self.status_filter)
        search_layout.addStretch()

        search_frame.setLayout(search_layout)
        main_layout.addWidget(search_frame)

        # جدول المشتريات
        self.purchases_table = QTableWidget()
        self.purchases_table.setColumnCount(9)
        self.purchases_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "المورد", "التاريخ", "المنتج",
            "الكمية", "سعر الوحدة", "الإجمالي", "الحالة", "ملاحظات"
        ])

        # إعداد التحديد للسطر كاملاً
        self.purchases_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.purchases_table.setSelectionMode(QAbstractItemView.SingleSelection)

        # تصميم الجدول
        self.purchases_table.setStyleSheet("""
            QTableWidget {
                background-color: #ffffff;
                border: 3px solid #000000;
                border-radius: 10px;
                gridline-color: #e5e7eb;
                font-size: 12px;
                selection-background-color: #3b82f6;
                selection-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e5e7eb;
                font-weight: bold;
                color: #000000;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4f46e5,
                    stop:1 #3730a3);
                color: white;
                padding: 10px;
                border: 2px solid #1e1b4b;
                font-weight: bold;
                font-size: 13px;
            }
        """)

        # تعديل عرض الأعمدة
        header = self.purchases_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # رقم الفاتورة
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # المورد
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # التاريخ
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # المنتج
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # الكمية
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # سعر الوحدة
        header.setSectionResizeMode(6, QHeaderView.Fixed)  # الإجمالي
        header.setSectionResizeMode(7, QHeaderView.Fixed)  # الحالة
        header.setSectionResizeMode(8, QHeaderView.Stretch)  # ملاحظات

        # تحديد عرض الأعمدة
        self.purchases_table.setColumnWidth(0, 100)  # رقم الفاتورة
        self.purchases_table.setColumnWidth(2, 120)  # التاريخ
        self.purchases_table.setColumnWidth(4, 80)   # الكمية
        self.purchases_table.setColumnWidth(5, 100)  # سعر الوحدة
        self.purchases_table.setColumnWidth(6, 100)  # الإجمالي
        self.purchases_table.setColumnWidth(7, 80)   # الحالة

        main_layout.addWidget(self.purchases_table, 1)

        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 5px;
                max-height: 70px;
                min-height: 65px;
            }
        """)

        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(10, 5, 10, 5)
        buttons_layout.setSpacing(10)

        # أزرار العمليات مثل المخزون مع ألوان متنوعة
        self.add_button = QPushButton("➕ إضافة مشترى")
        self.style_advanced_button(self.add_button, 'emerald', has_menu=True)  # أخضر زمردي مميز مع قائمة
        self.add_button.clicked.connect(self.add_purchase)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'primary')  # أزرق كلاسيكي
        self.edit_button.clicked.connect(self.edit_purchase)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')  # أحمر تحذيري
        self.delete_button.clicked.connect(self.delete_purchase)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # تصميم حديث ومتطور
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الثانية - العمليات المتقدمة
        self.view_button = QPushButton("👁️ عرض التفاصيل ▼")
        self.style_advanced_button(self.view_button, 'indigo', has_menu=True)  # بنفسجي للتفاصيل
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة لعرض التفاصيل
        from ui.unified_styles import UnifiedStyles
        view_menu = QMenu(self)
        view_menu.setStyleSheet(UnifiedStyles.get_menu_style('indigo', 'normal'))

        view_details_action = QAction("👁️ عرض التفاصيل", self)
        view_details_action.triggered.connect(self.view_purchase_details)
        view_menu.addAction(view_details_action)

        purchase_history_action = QAction("📊 تاريخ المشتريات", self)
        purchase_history_action.triggered.connect(self.view_purchase_history)
        view_menu.addAction(purchase_history_action)

        supplier_info_action = QAction("🏪 معلومات المورد", self)
        supplier_info_action.triggered.connect(self.view_supplier_info)
        view_menu.addAction(supplier_info_action)

        self.view_button.setMenu(view_menu)

        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'info', has_menu=True)  # لون متسق مع نظام الألوان
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة للتصدير
        export_menu = QMenu(self)
        export_menu.setStyleSheet(UnifiedStyles.get_menu_style('info', 'normal'))

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(self.export_to_excel)
        export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(self.export_to_pdf)
        export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(self.export_to_csv)
        export_menu.addAction(csv_action)

        json_action = QAction("🔗 تصدير إلى JSON", self)
        json_action.triggered.connect(self.export_to_json)
        export_menu.addAction(json_action)

        self.export_button.setMenu(export_menu)

        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')  # وردي للإحصائيات
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إضافة ملخص المشتريات محسن مثل المخزون
        self.total_purchases_label = QLabel("إجمالي المشتريات: 0 | القيمة الإجمالية: 0.00 ج.م")
        self.total_purchases_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                padding: 8px 16px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3b82f6,
                    stop:0.5 #2563eb,
                    stop:1 #1d4ed8);
                border: 3px solid #1e40af;
                border-radius: 12px;
                min-height: 34px;
                max-height: 38px;
            }
        """)
        self.total_purchases_label.setAlignment(Qt.AlignCenter)

        # إضافة الأزرار للتخطيط
        buttons_layout.addWidget(self.add_button)
        buttons_layout.addWidget(self.edit_button)
        buttons_layout.addWidget(self.delete_button)
        buttons_layout.addWidget(self.refresh_button)
        buttons_layout.addWidget(self.view_button)
        buttons_layout.addWidget(self.export_button)
        buttons_layout.addWidget(self.statistics_button)
        buttons_layout.addWidget(self.total_purchases_label)

        buttons_frame.setLayout(buttons_layout)
        main_layout.addWidget(buttons_frame)

        self.setLayout(main_layout)

    def filter_purchases(self):
        """تصفية المشتريات"""
        search_text = self.search_edit.text().lower()
        status_filter = self.status_filter.currentText()
        
        for row in range(self.purchases_table.rowCount()):
            show_row = True
            
            # فلترة النص
            if search_text:
                row_text = ""
                for col in range(self.purchases_table.columnCount()):
                    item = self.purchases_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "
                
                if search_text not in row_text:
                    show_row = False
            
            # فلترة الحالة
            if status_filter != "جميع الحالات":
                status_item = self.purchases_table.item(row, 7)
                if status_item and status_item.text() != status_filter:
                    show_row = False
            
            self.purchases_table.setRowHidden(row, not show_row)
        
        # تحديث الملخص بعد التصفية
        self.update_summary()

    def create_sample_data(self):
        """إنشاء بيانات تجريبية للمشتريات"""
        sample_data = [
            ["P001", "شركة الدهانات المتحدة", "2024-01-15", "دهان أبيض", "50", "25.00", "1250.00", "مكتملة", "تم التسليم"],
            ["P002", "مؤسسة السيراميك", "2024-01-16", "سيراميك أرضي", "100", "15.00", "1500.00", "معلقة", "في الانتظار"],
            ["P003", "شركة الأخشاب", "2024-01-17", "خشب صنوبر", "20", "100.00", "2000.00", "مكتملة", "تم التسليم"],
            ["P004", "مؤسسة الكهرباء", "2024-01-18", "مفاتيح كهربائية", "200", "5.00", "1000.00", "مكتملة", "تم التسليم"],
            ["P005", "شركة الأدوات الصحية", "2024-01-19", "حنفيات مياه", "30", "45.00", "1350.00", "معلقة", "قيد المراجعة"]
        ]

        self.purchases_table.setRowCount(len(sample_data))

        for row, data in enumerate(sample_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(str(value))
                item.setFont(QFont("Arial", 10, QFont.Bold))
                item.setForeground(QColor("#000000"))

                # تلوين حسب الحالة
                if col == 7:  # عمود الحالة
                    if value == "مكتملة":
                        item.setForeground(QColor("#059669"))
                    elif value == "معلقة":
                        item.setForeground(QColor("#d97706"))
                    elif value == "ملغية":
                        item.setForeground(QColor("#dc2626"))

                self.purchases_table.setItem(row, col, item)

        # تحديث الملخص
        self.update_summary()

    def add_purchase(self):
        """إضافة مشترى جديد"""
        QMessageBox.information(self, "قريباً", "ميزة إضافة مشترى جديد ستكون متاحة قريباً!")

    def edit_purchase(self):
        """تعديل مشترى"""
        current_row = self.purchases_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار مشترى للتعديل")
            return

        # الحصول على بيانات السطر المحدد
        invoice_number = self.purchases_table.item(current_row, 0).text() if self.purchases_table.item(current_row, 0) else ""
        supplier = self.purchases_table.item(current_row, 1).text() if self.purchases_table.item(current_row, 1) else ""
        date = self.purchases_table.item(current_row, 2).text() if self.purchases_table.item(current_row, 2) else ""
        product = self.purchases_table.item(current_row, 3).text() if self.purchases_table.item(current_row, 3) else ""
        quantity = self.purchases_table.item(current_row, 4).text() if self.purchases_table.item(current_row, 4) else ""
        unit_price = self.purchases_table.item(current_row, 5).text() if self.purchases_table.item(current_row, 5) else ""
        total = self.purchases_table.item(current_row, 6).text() if self.purchases_table.item(current_row, 6) else ""
        status = self.purchases_table.item(current_row, 7).text() if self.purchases_table.item(current_row, 7) else ""
        notes = self.purchases_table.item(current_row, 8).text() if self.purchases_table.item(current_row, 8) else ""

        # إنشاء نافذة التعديل
        dialog = QDialog(self)
        dialog.setWindowTitle(f"✏️ تعديل المشترى: {invoice_number}")
        dialog.setModal(True)
        dialog.resize(500, 600)

        layout = QVBoxLayout()

        # حقول التعديل
        form_layout = QFormLayout()

        invoice_edit = QLineEdit(invoice_number)
        supplier_edit = QLineEdit(supplier)
        date_edit = QLineEdit(date)
        product_edit = QLineEdit(product)
        quantity_edit = QLineEdit(quantity)
        unit_price_edit = QLineEdit(unit_price)
        total_edit = QLineEdit(total)
        status_combo = QComboBox()
        status_combo.addItems(["مكتمل", "معلق", "ملغي"])
        status_combo.setCurrentText(status)
        notes_edit = QTextEdit(notes)
        notes_edit.setMaximumHeight(100)

        form_layout.addRow("رقم الفاتورة:", invoice_edit)
        form_layout.addRow("المورد:", supplier_edit)
        form_layout.addRow("التاريخ:", date_edit)
        form_layout.addRow("المنتج:", product_edit)
        form_layout.addRow("الكمية:", quantity_edit)
        form_layout.addRow("سعر الوحدة:", unit_price_edit)
        form_layout.addRow("الإجمالي:", total_edit)
        form_layout.addRow("الحالة:", status_combo)
        form_layout.addRow("ملاحظات:", notes_edit)

        layout.addLayout(form_layout)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()

        save_button = QPushButton("💾 حفظ التعديلات")
        save_button.clicked.connect(lambda: self.save_purchase_changes(
            current_row, invoice_edit.text(), supplier_edit.text(), date_edit.text(),
            product_edit.text(), quantity_edit.text(), unit_price_edit.text(),
            total_edit.text(), status_combo.currentText(), notes_edit.toPlainText(), dialog
        ))
        buttons_layout.addWidget(save_button)

        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.clicked.connect(dialog.close)
        buttons_layout.addWidget(cancel_button)

        layout.addLayout(buttons_layout)
        dialog.setLayout(layout)
        dialog.exec_()

    def save_purchase_changes(self, row, invoice, supplier, date, product, quantity, unit_price, total, status, notes, dialog):
        """حفظ تعديلات المشترى"""
        try:
            # تحديث البيانات في الجدول
            self.purchases_table.setItem(row, 0, QTableWidgetItem(invoice))
            self.purchases_table.setItem(row, 1, QTableWidgetItem(supplier))
            self.purchases_table.setItem(row, 2, QTableWidgetItem(date))
            self.purchases_table.setItem(row, 3, QTableWidgetItem(product))
            self.purchases_table.setItem(row, 4, QTableWidgetItem(quantity))
            self.purchases_table.setItem(row, 5, QTableWidgetItem(unit_price))
            self.purchases_table.setItem(row, 6, QTableWidgetItem(total))
            self.purchases_table.setItem(row, 7, QTableWidgetItem(status))
            self.purchases_table.setItem(row, 8, QTableWidgetItem(notes))

            # تطبيق التنسيق
            for col in range(9):
                item = self.purchases_table.item(row, col)
                if item:
                    item.setFont(QFont("Arial", 10, QFont.Bold))
                    item.setForeground(QColor("#000000"))

                    # تلوين حسب الحالة
                    if col == 7:  # عمود الحالة
                        if status == "مكتمل":
                            item.setBackground(QColor("#d1fae5"))
                        elif status == "معلق":
                            item.setBackground(QColor("#fef3c7"))
                        elif status == "ملغي":
                            item.setBackground(QColor("#fee2e2"))

            QMessageBox.information(self, "تم", "تم حفظ التعديلات بنجاح!")
            dialog.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ التعديلات: {str(e)}")

    def delete_purchase(self):
        """حذف مشترى"""
        current_row = self.purchases_table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(self, "تأكيد الحذف", "هل أنت متأكد من حذف هذا المشترى؟")
            if reply == QMessageBox.Yes:
                self.purchases_table.removeRow(current_row)
                self.update_summary()
                QMessageBox.information(self, "نجح", "تم حذف المشترى بنجاح!")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مشترى للحذف")

    def refresh_data(self):
        """تحديث البيانات"""
        try:
            # حفظ السطر المحدد حالياً
            current_row = self.purchases_table.currentRow()

            # إعادة تحميل البيانات
            self.create_sample_data()
            self.update_summary()

            # استعادة التحديد إذا كان ممكناً
            if current_row >= 0 and current_row < self.purchases_table.rowCount():
                self.purchases_table.selectRow(current_row)

            # إظهار رسالة نجاح
            QMessageBox.information(self, "تم التحديث", "🔄 تم تحديث بيانات المشتريات بنجاح!")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحديث البيانات: {str(e)}")

    def update_summary(self):
        """تحديث ملخص المشتريات"""
        try:
            total_count = 0
            total_value = 0.0
            
            for row in range(self.purchases_table.rowCount()):
                if not self.purchases_table.isRowHidden(row):
                    total_count += 1
                    total_item = self.purchases_table.item(row, 6)  # عمود الإجمالي
                    if total_item:
                        total_value += float(total_item.text())
            
            self.total_purchases_label.setText(f"إجمالي المشتريات: {total_count} | القيمة الإجمالية: {total_value:.2f} ج.م")
            
        except Exception as e:
            print(f"خطأ في تحديث الملخص: {str(e)}")

    def show_statistics(self):
        """عرض إحصائيات المشتريات"""
        try:
            from database import Purchase, get_session
            session = get_session()
            purchases = session.query(Purchase).all()

            if not purchases:
                QMessageBox.information(self, "إحصائيات المشتريات", "لا توجد مشتريات لعرض الإحصائيات")
                return

            # حساب الإحصائيات
            total_purchases = len(purchases)
            total_amount = sum(purchase.total_amount or 0 for purchase in purchases)
            avg_amount = total_amount / total_purchases if total_purchases > 0 else 0

            # إحصائيات حسب المورد
            supplier_stats = {}
            for purchase in purchases:
                supplier_name = purchase.supplier.name if purchase.supplier else 'غير محدد'
                if supplier_name in supplier_stats:
                    supplier_stats[supplier_name]['count'] += 1
                    supplier_stats[supplier_name]['amount'] += purchase.total_amount or 0
                else:
                    supplier_stats[supplier_name] = {
                        'count': 1,
                        'amount': purchase.total_amount or 0
                    }

            # إحصائيات حسب الحالة
            status_stats = {}
            for purchase in purchases:
                status = purchase.status or 'غير محدد'
                if status in status_stats:
                    status_stats[status] += 1
                else:
                    status_stats[status] = 1

            # إنشاء نافذة الإحصائيات
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextBrowser, QPushButton, QHBoxLayout

            dialog = QDialog(self)
            dialog.setWindowTitle("📊 إحصائيات المشتريات")
            dialog.setModal(True)
            dialog.resize(700, 600)

            layout = QVBoxLayout()

            # الإحصائيات العامة
            general_stats = f"""
📊 الإحصائيات العامة:
─────────────────────────────────────────────────────────────────────────────
🛒 إجمالي المشتريات: {total_purchases}
💰 إجمالي المبلغ: {int(total_amount):,} جنيه
📈 متوسط قيمة المشترى: {int(avg_amount):,} جنيه

🏪 توزيع حسب المورد:
─────────────────────────────────────────────────────────────────────────────
"""

            # أفضل 5 موردين
            sorted_suppliers = sorted(supplier_stats.items(), key=lambda x: x[1]['amount'], reverse=True)[:5]
            for supplier, stats in sorted_suppliers:
                percentage = (stats['count'] / total_purchases) * 100
                general_stats += f"• {supplier}: {stats['count']} مشترى ({percentage:.1f}%) - {int(stats['amount']):,} جنيه\n"

            general_stats += f"""
─────────────────────────────────────────────────────────────────────────────

📋 توزيع حسب الحالة:
─────────────────────────────────────────────────────────────────────────────
"""

            for status, count in status_stats.items():
                percentage = (count / total_purchases) * 100
                general_stats += f"• {status}: {count} مشترى ({percentage:.1f}%)\n"

            # عرض الإحصائيات
            stats_text = QTextBrowser()
            stats_text.setPlainText(general_stats)
            stats_text.setStyleSheet("""
                QTextBrowser {
                    font-family: 'Segoe UI', Arial, sans-serif;
                    font-size: 11px;
                    line-height: 1.4;
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 15px;
                }
            """)
            layout.addWidget(stats_text)

            # أزرار الإجراءات
            buttons_layout = QHBoxLayout()

            export_stats_btn = QPushButton("📤 تصدير الإحصائيات")
            export_stats_btn.clicked.connect(lambda: self.export_statistics_report(general_stats))
            buttons_layout.addWidget(export_stats_btn)

            close_button = QPushButton("❌ إغلاق")
            close_button.clicked.connect(dialog.close)
            buttons_layout.addWidget(close_button)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في عرض الإحصائيات: {str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة مطابق للفواتير"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق للفواتير
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#047857', 'hover_mid': '#059669', 'hover_end': '#10b981', 'hover_bottom': '#34d399',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#052e16', 'pressed_border': '#064e3b',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.5)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#991b1b', 'hover_mid': '#dc2626', 'hover_end': '#ef4444', 'hover_bottom': '#f87171',
                    'hover_border': '#ef4444', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.5)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#14b8a6',
                    'hover_start': '#134e4a', 'hover_mid': '#0d9488', 'hover_end': '#14b8a6', 'hover_bottom': '#2dd4bf',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.5)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#6366f1',
                    'hover_start': '#312e81', 'hover_mid': '#4f46e5', 'hover_end': '#6366f1', 'hover_bottom': '#818cf8',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4f46e5', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.5)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0284c7', 'bg_bottom': '#0ea5e9',
                    'hover_start': '#075985', 'hover_mid': '#0891b2', 'hover_end': '#0ea5e9', 'hover_bottom': '#38bdf8',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0284c7', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.5)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#ec4899',
                    'hover_start': '#831843', 'hover_mid': '#be185d', 'hover_end': '#ec4899', 'hover_bottom': '#f472b6',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.5)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات - مطابق للفواتير
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']},
                               0 0 30px rgba(255, 255, 255, 0.1);
                    letter-spacing: 0.3px;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 4px solid {color_scheme['hover_border']};
                    transform: translateY(-2px);
                    box-shadow: 0 8px 20px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 25px {color_scheme['shadow']},
                               0 0 40px rgba(255, 255, 255, 0.15);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px);
                    box-shadow: 0 3px 8px {color_scheme['shadow']},
                               inset 0 1px 0 rgba(255, 255, 255, 0.2),
                               inset 0 -1px 0 rgba(0, 0, 0, 0.5),
                               0 0 15px {color_scheme['shadow']};
                }}
                QPushButton::menu-indicator {{
                    {f"image: none; width: 0px;" if not has_menu else "width: 12px; height: 12px; margin-right: 4px;"}
                }}
            """

            button.setStyleSheet(style)

        except Exception as e:
            print(f"❌ خطأ في تطبيق تصميم الزر المتطور: {str(e)}")
                }
                QPushButton:hover {
                    background-color: #2563eb;
                }
            """)

    def export_to_excel(self):
        """تصدير المشتريات إلى Excel (CSV)"""
        self.export_to_csv()

    def export_to_csv(self):
        """تصدير المشتريات إلى CSV"""
        try:
            import csv
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ ملف CSV", "المشتريات.csv", "CSV Files (*.csv)")
            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)
                    headers = [self.purchases_table.horizontalHeaderItem(col).text() for col in range(self.purchases_table.columnCount())]
                    writer.writerow(headers)
                    for row in range(self.purchases_table.rowCount()):
                        row_data = [self.purchases_table.item(row, col).text() if self.purchases_table.item(row, col) else "" for col in range(self.purchases_table.columnCount())]
                        writer.writerow(row_data)
                QMessageBox.information(self, "تم", f"تم تصدير المشتريات بنجاح إلى:\n{file_path}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في التصدير: {str(e)}")

    def export_to_pdf(self):
        """تصدير المشتريات إلى PDF"""
        try:
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QTextDocument
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtCore import QDate

            # حفظ ملف PDF مباشرة
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير المشتريات", "تقرير_المشتريات.pdf", "PDF Files (*.pdf)"
            )

            if file_path:
                # إنشاء محتوى HTML
                html_content = f"""
                <html dir="rtl">
                <head>
                    <meta charset="utf-8">
                    <title>تقرير المشتريات</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 20px; }}
                        h1 {{ color: #6366f1; text-align: center; }}
                        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                        th {{ background-color: #f2f2f2; }}
                    </style>
                </head>
                <body>
                    <h1>🛒 تقرير المشتريات</h1>
                    <p><strong>تاريخ التقرير:</strong> {QDate.currentDate().toString('yyyy-MM-dd')}</p>

                    <table>
                        <tr>
                            <th>الرقم</th>
                            <th>رقم المشترى</th>
                            <th>المورد</th>
                            <th>التاريخ</th>
                            <th>المبلغ الإجمالي</th>
                            <th>الحالة</th>
                        </tr>
                """

                total_amount = 0
                for row in range(self.purchases_table.rowCount()):
                    purchase_id = self.purchases_table.item(row, 0).text() if self.purchases_table.item(row, 0) else ""
                    purchase_number = self.purchases_table.item(row, 1).text() if self.purchases_table.item(row, 1) else ""
                    supplier = self.purchases_table.item(row, 2).text() if self.purchases_table.item(row, 2) else ""
                    date = self.purchases_table.item(row, 3).text() if self.purchases_table.item(row, 3) else ""
                    amount_text = self.purchases_table.item(row, 4).text() if self.purchases_table.item(row, 4) else "0"
                    status = self.purchases_table.item(row, 5).text() if self.purchases_table.item(row, 5) else ""

                    try:
                        amount = float(amount_text.replace(',', '').replace('جنيه', '').strip())
                        total_amount += amount
                    except:
                        amount = 0

                    html_content += f"""
                        <tr>
                            <td>{purchase_id}</td>
                            <td>{purchase_number}</td>
                            <td>{supplier}</td>
                            <td>{date}</td>
                            <td>{int(amount):,} جنيه</td>
                            <td>{status}</td>
                        </tr>
                    """

                html_content += f"""
                    </table>
                    <h3>إجمالي المشتريات: {int(total_amount):,} جنيه</h3>
                </body>
                </html>
                """

                # إنشاء طابعة PDF
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)
                printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

                # إنشاء مستند وطباعته إلى PDF
                document = QTextDocument()
                document.setHtml(html_content)
                document.print_(printer)

                QMessageBox.information(self, "تم", f"تم تصدير المشتريات إلى PDF بنجاح:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تصدير PDF: {str(e)}")

    def export_to_json(self):
        """تصدير المشتريات إلى JSON"""
        try:
            import json
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف JSON", "المشتريات.json", "JSON Files (*.json)"
            )

            if file_path:
                data = []
                for row in range(self.purchases_table.rowCount()):
                    row_data = {
                        'الرقم': self.purchases_table.item(row, 0).text() if self.purchases_table.item(row, 0) else "",
                        'رقم المشترى': self.purchases_table.item(row, 1).text() if self.purchases_table.item(row, 1) else "",
                        'المورد': self.purchases_table.item(row, 2).text() if self.purchases_table.item(row, 2) else "",
                        'التاريخ': self.purchases_table.item(row, 3).text() if self.purchases_table.item(row, 3) else "",
                        'المبلغ الإجمالي': self.purchases_table.item(row, 4).text() if self.purchases_table.item(row, 4) else "",
                        'الحالة': self.purchases_table.item(row, 5).text() if self.purchases_table.item(row, 5) else ""
                    }
                    data.append(row_data)

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)

                QMessageBox.information(self, "تم", f"تم تصدير المشتريات إلى JSON بنجاح:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تصدير JSON: {str(e)}")

    def export_statistics_report(self, stats_content):
        """تصدير تقرير الإحصائيات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtCore import QDate

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الإحصائيات", "إحصائيات_المشتريات.txt", "Text Files (*.txt)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"""
═══════════════════════════════════════════════════════════════════════════════
                            📊 تقرير إحصائيات المشتريات
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الإنشاء: {QDate.currentDate().toString('hh:mm:ss')}

{stats_content}

═══════════════════════════════════════════════════════════════════════════════
                        تم إنشاء التقرير بواسطة نظام إدارة المشتريات
═══════════════════════════════════════════════════════════════════════════════
""")

                QMessageBox.information(self, "تم", f"تم تصدير تقرير الإحصائيات بنجاح إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تصدير التقرير: {str(e)}")

    def view_purchase_details(self):
        """عرض تفاصيل المشترى المحدد"""
        selected_row = self.purchases_table.currentRow()
        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار مشترى من القائمة")
            return

        # الحصول على جميع بيانات السطر المحدد
        invoice_number = self.purchases_table.item(selected_row, 0).text() if self.purchases_table.item(selected_row, 0) else "غير محدد"
        supplier = self.purchases_table.item(selected_row, 1).text() if self.purchases_table.item(selected_row, 1) else "غير محدد"
        date = self.purchases_table.item(selected_row, 2).text() if self.purchases_table.item(selected_row, 2) else "غير محدد"
        product = self.purchases_table.item(selected_row, 3).text() if self.purchases_table.item(selected_row, 3) else "غير محدد"
        quantity = self.purchases_table.item(selected_row, 4).text() if self.purchases_table.item(selected_row, 4) else "0"
        unit_price = self.purchases_table.item(selected_row, 5).text() if self.purchases_table.item(selected_row, 5) else "0"
        total = self.purchases_table.item(selected_row, 6).text() if self.purchases_table.item(selected_row, 6) else "0"
        status = self.purchases_table.item(selected_row, 7).text() if self.purchases_table.item(selected_row, 7) else "غير محدد"
        notes = self.purchases_table.item(selected_row, 8).text() if self.purchases_table.item(selected_row, 8) else "لا توجد ملاحظات"

        # إنشاء نافذة التفاصيل
        dialog = QDialog(self)
        dialog.setWindowTitle(f"👁️ تفاصيل المشترى: {invoice_number}")
        dialog.setModal(True)
        dialog.resize(600, 500)

        layout = QVBoxLayout()

        # معلومات المشترى
        details_text = f"""
🛒 تفاصيل المشترى:
─────────────────────────────────────────────────────────────────────────────
📋 رقم الفاتورة: {invoice_number}
🏪 المورد: {supplier}
📅 التاريخ: {date}
📦 المنتج: {product}
📊 الكمية: {quantity}
💰 سعر الوحدة: {unit_price}
💵 الإجمالي: {total}
📋 الحالة: {status}
📝 ملاحظات: {notes}
─────────────────────────────────────────────────────────────────────────────

💡 معلومات إضافية:
• تم إنشاء هذا السجل في النظام
• يمكن تعديل البيانات من خلال زر التعديل
• يمكن تصدير هذه البيانات مع التقارير
        """

        # عرض التفاصيل
        from PyQt5.QtWidgets import QTextBrowser
        details_browser = QTextBrowser()
        details_browser.setPlainText(details_text)
        details_browser.setStyleSheet("""
            QTextBrowser {
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 11px;
                line-height: 1.4;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        layout.addWidget(details_browser)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        edit_button = QPushButton("✏️ تعديل")
        edit_button.clicked.connect(lambda: (dialog.close(), self.edit_purchase()))
        buttons_layout.addWidget(edit_button)

        print_button = QPushButton("🖨️ طباعة")
        print_button.clicked.connect(lambda: self.print_purchase_details(invoice_number, supplier, date, product, quantity, unit_price, total, status, notes))
        buttons_layout.addWidget(print_button)

        close_button = QPushButton("❌ إغلاق")
        close_button.clicked.connect(dialog.close)
        buttons_layout.addWidget(close_button)

        layout.addLayout(buttons_layout)
        dialog.setLayout(layout)
        dialog.exec_()

    def print_purchase_details(self, invoice, supplier, date, product, quantity, unit_price, total, status, notes):
        """طباعة تفاصيل المشترى"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtCore import QDate

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تفاصيل المشترى", f"تفاصيل_المشترى_{invoice}.txt", "Text Files (*.txt)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"""
═══════════════════════════════════════════════════════════════════════════════
                            🛒 تفاصيل المشترى
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ الطباعة: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الطباعة: {QDate.currentDate().toString('hh:mm:ss')}

📋 معلومات المشترى:
─────────────────────────────────────────────────────────────────────────────
رقم الفاتورة: {invoice}
المورد: {supplier}
التاريخ: {date}
المنتج: {product}
الكمية: {quantity}
سعر الوحدة: {unit_price}
الإجمالي: {total}
الحالة: {status}
ملاحظات: {notes}

═══════════════════════════════════════════════════════════════════════════════
                        تم إنشاء التقرير بواسطة نظام إدارة المشتريات
═══════════════════════════════════════════════════════════════════════════════
""")

                QMessageBox.information(self, "تم", f"تم حفظ تفاصيل المشترى بنجاح إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ التفاصيل: {str(e)}")

    def view_purchase_history(self):
        """عرض تاريخ المشتريات"""
        try:
            # إنشاء نافذة تاريخ المشتريات
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QLabel, QDateEdit, QComboBox
            from PyQt5.QtCore import QDate

            dialog = QDialog(self)
            dialog.setWindowTitle("📊 تاريخ المشتريات")
            dialog.setModal(True)
            dialog.resize(900, 600)

            layout = QVBoxLayout()

            # فلاتر التاريخ
            filter_layout = QHBoxLayout()

            filter_layout.addWidget(QLabel("من تاريخ:"))
            from_date = QDateEdit()
            from_date.setDate(QDate.currentDate().addDays(-30))  # آخر 30 يوم
            from_date.setCalendarPopup(True)
            filter_layout.addWidget(from_date)

            filter_layout.addWidget(QLabel("إلى تاريخ:"))
            to_date = QDateEdit()
            to_date.setDate(QDate.currentDate())
            to_date.setCalendarPopup(True)
            filter_layout.addWidget(to_date)

            filter_layout.addWidget(QLabel("المورد:"))
            supplier_filter = QComboBox()
            supplier_filter.addItem("جميع الموردين")
            # إضافة الموردين من البيانات الحالية
            suppliers = set()
            for row in range(self.purchases_table.rowCount()):
                supplier_item = self.purchases_table.item(row, 1)
                if supplier_item:
                    suppliers.add(supplier_item.text())
            for supplier in sorted(suppliers):
                supplier_filter.addItem(supplier)
            filter_layout.addWidget(supplier_filter)

            filter_button = QPushButton("🔍 تطبيق الفلتر")
            filter_layout.addWidget(filter_button)

            layout.addLayout(filter_layout)

            # جدول التاريخ
            history_table = QTableWidget()
            history_table.setColumnCount(8)
            history_table.setHorizontalHeaderLabels([
                "التاريخ", "رقم الفاتورة", "المورد", "المنتج",
                "الكمية", "سعر الوحدة", "الإجمالي", "الحالة"
            ])

            # إعداد التحديد للسطر كاملاً
            history_table.setSelectionBehavior(QAbstractItemView.SelectRows)
            history_table.setSelectionMode(QAbstractItemView.SingleSelection)

            # تحميل البيانات
            def load_history_data():
                # محاكاة بيانات تاريخية
                history_data = []
                for row in range(self.purchases_table.rowCount()):
                    row_data = []
                    for col in [2, 0, 1, 3, 4, 5, 6, 7]:  # إعادة ترتيب الأعمدة
                        item = self.purchases_table.item(row, col)
                        row_data.append(item.text() if item else "")
                    history_data.append(row_data)

                # ترتيب حسب التاريخ (الأحدث أولاً)
                history_data.sort(key=lambda x: x[0], reverse=True)

                history_table.setRowCount(len(history_data))
                for row, data in enumerate(history_data):
                    for col, value in enumerate(data):
                        item = QTableWidgetItem(str(value))
                        item.setFont(QFont("Arial", 10, QFont.Bold))

                        # تلوين حسب الحالة
                        if col == 7:  # عمود الحالة
                            if value == "مكتمل":
                                item.setBackground(QColor("#d1fae5"))
                            elif value == "معلق":
                                item.setBackground(QColor("#fef3c7"))
                            elif value == "ملغي":
                                item.setBackground(QColor("#fee2e2"))

                        history_table.setItem(row, col, item)

                # تعديل عرض الأعمدة
                header = history_table.horizontalHeader()
                header.setStretchLastSection(True)
                for i in range(history_table.columnCount()):
                    header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

            # تحميل البيانات الأولية
            load_history_data()

            # ربط زر الفلتر
            filter_button.clicked.connect(load_history_data)

            layout.addWidget(history_table)

            # إحصائيات سريعة
            stats_layout = QHBoxLayout()

            total_purchases = history_table.rowCount()
            total_amount = 0
            for row in range(history_table.rowCount()):
                amount_item = history_table.item(row, 6)
                if amount_item:
                    try:
                        amount = float(amount_item.text().replace(',', '').replace('جنيه', '').strip())
                        total_amount += amount
                    except:
                        pass

            stats_label = QLabel(f"📊 إجمالي المشتريات: {total_purchases} | 💰 إجمالي المبلغ: {int(total_amount):,} جنيه")
            stats_label.setStyleSheet("font-weight: bold; font-size: 12px; padding: 10px; background-color: #f0f9ff; border-radius: 5px;")
            stats_layout.addWidget(stats_label)

            layout.addLayout(stats_layout)

            # أزرار الإجراءات
            buttons_layout = QHBoxLayout()

            export_history_btn = QPushButton("📤 تصدير التاريخ")
            export_history_btn.clicked.connect(lambda: self.export_purchase_history(history_table))
            buttons_layout.addWidget(export_history_btn)

            close_btn = QPushButton("❌ إغلاق")
            close_btn.clicked.connect(dialog.close)
            buttons_layout.addWidget(close_btn)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في عرض تاريخ المشتريات: {str(e)}")

    def export_purchase_history(self, history_table):
        """تصدير تاريخ المشتريات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtCore import QDate
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تاريخ المشتريات", "تاريخ_المشتريات.csv", "CSV Files (*.csv)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة العناوين
                    headers = []
                    for col in range(history_table.columnCount()):
                        headers.append(history_table.horizontalHeaderItem(col).text())
                    writer.writerow(headers)

                    # كتابة البيانات
                    for row in range(history_table.rowCount()):
                        row_data = []
                        for col in range(history_table.columnCount()):
                            item = history_table.item(row, col)
                            row_data.append(item.text() if item else "")
                        writer.writerow(row_data)

                QMessageBox.information(self, "تم", f"تم تصدير تاريخ المشتريات بنجاح إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تصدير التاريخ: {str(e)}")

    def view_supplier_info(self):
        """عرض معلومات المورد"""
        selected_row = self.purchases_table.currentRow()
        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار مشترى من القائمة")
            return

        supplier = self.purchases_table.item(selected_row, 2).text() if self.purchases_table.item(selected_row, 2) else ""
        QMessageBox.information(self, "معلومات المورد", f"معلومات المورد: {supplier}")
